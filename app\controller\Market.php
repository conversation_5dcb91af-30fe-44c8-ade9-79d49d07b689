<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\App as AppModel;
use app\service\YmService;
use app\model\AppRuntime;
use app\model\AppSku;
use think\facade\Db;

/**
 * 市场控制器
 * 无需鉴权，所有用户都能访问
 */
class Market extends BaseController
{
    /**
     * 控制器中间件
     * 不需要Auth中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 市场首页
     * 显示所有已上线的应用列表
     */
    public function index(Request $request)
    {
        // 查询所有已上线的应用列表
        $list = AppModel::where('isOnline', 1)->where('isPrivate',0)->where('status',2)
            ->order('createTime', 'desc')
            ->paginate([
                'list_rows' => 12,
                'query' => $request->param(),
            ]);

        // 获取YmService配置
        $ymConfig = YmService::getConfig();

        // 模板赋值
        View::assign([
            'list' => $list,
            'ymConfig' => $ymConfig,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 应用详情
     */
    public function detail(Request $request)
    {
        // 获取应用ID
        $appId = $request->param('appId');

        if (!$appId) {
            return redirect('/market');
        }

        // 查询应用信息
        $app = AppModel::find($appId);

        if (!$app || $app->isOnline != 1) {
            return redirect('/market');
        }

        // 获取运行时环境列表
        $runtimes = $app->runtimes()->select()->toArray();



        // 获取SKU列表
        $skus = $app->skus()->order('sort', 'asc')->select();

        // 为每个SKU添加关联的运行时环境信息
        foreach ($skus as &$sku) {
            // 获取SKU关联的运行时环境ID列表
            $appRuntimeIds = Db::name('app_sku_runtime')
                ->where('appSkuId', $sku->appSkuId)
                ->column('appRuntimeId');

            // 获取运行时环境详情
            $skuRuntimes = [];
            if (!empty($appRuntimeIds)) {
                $skuRuntimes = AppRuntime::whereIn('appRuntimeId', $appRuntimeIds)
                    ->column('runtimeId');
            }

            // 添加到SKU对象
            $sku->runtimeIds = $skuRuntimes;
        }

        // 获取YmService配置
        $ymConfig = YmService::getConfig();

        // 获取插件列表
        $ymPlugins = YmService::getPlguns();

        // 提取运行时环境名称和图标
        $runtimeNames = [];
        $runtimeIcons = [];

        // 遍历配置中的运行时环境
        $runtimePluginKeys = [];
        foreach ($ymConfig['runtime'] as $runtimeGroup) {
            foreach ($runtimeGroup['runtime'] as $runtime) {
                $runtimeNames[$runtime['id']] = $runtime['label'];
                $runtimeIcons[$runtime['id']] = $runtime['icon'];
                $runtimePluginKeys[$runtime['id']] = $runtime['pluginKey'];
            }
        }

        // 为每个环境添加所需的插件列表
        foreach ($runtimes as &$runtime) {

            $pluginKey = $runtimePluginKeys[$runtime['runtimeId']] ?? '';
            $runtime['plugins'] = [];

            // 如果环境有对应的插件列表
            if (!empty($pluginKey) && isset($ymPlugins[$pluginKey])) {
                $pluginIds = $ymPlugins[$pluginKey];
                $requiredPluginIds = !empty($runtime['requiredPluginIds']) ? explode(',', $runtime['requiredPluginIds']) : [];

                // 获取插件详细信息
                foreach ($pluginIds as $pluginId) {
                    foreach ($ymPlugins['plugins'] as $plugin) {
                        if ($plugin['id'] == $pluginId) {
                            $plugin['isRequired'] = in_array($pluginId, $requiredPluginIds);
                            $runtime['plugins'][] = $plugin;
                            break;
                        }
                    }
                }
            }
        }

        // 模板赋值
        View::assign([
            'app' => $app,
            'runtimes' => $runtimes,
            'runtimeCount' => count($runtimes),
            'runtimeNames' => $runtimeNames,
            'runtimeIcons' => $runtimeIcons,
            'skus' => $skus,
            'skuCount' => count($skus),
            'ymConfig' => $ymConfig,
            'ymPlugins' => $ymPlugins,
        ]);

        // 渲染模板
        return View::fetch();
    }
}
