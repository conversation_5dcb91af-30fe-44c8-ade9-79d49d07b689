<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;
use think\facade\Db;

/**
 * App模型
 */
class App extends Model
{
    // 设置表名
    protected $name = 'app';

    // 设置主键
    protected $pk = 'appId';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 设置创建时间字段
    protected $createTime = 'createTime';

    // 设置更新时间字段
    protected $updateTime = false;

    // 设置JSON类型字段
    protected $json = ['configSchema', 'extra'];

    // 设置JSON数据返回数组
    protected $jsonAssoc = true;

    /**
     * 获取运行时环境列表
     */
    public function runtimes()
    {
        return $this->hasMany(AppRuntime::class, 'appId', 'appId');
    }

    /**
     * 获取应用包列表
     */
    public function builds()
    {
        return $this->hasMany(AppBuild::class, 'appId', 'appId');
    }

    /**
     * 获取SKU列表
     * 通过运行时环境和关联表获取
     */
    public function skus()
    {
        // 获取应用下的所有运行时环境ID
        $appRuntimeIds = $this->runtimes()->column('appRuntimeId');

        if (empty($appRuntimeIds)) {
            return AppSku::where('1=0'); // 返回空集合
        }

        // 获取关联的SKU ID列表
        $appSkuIds = Db::name('app_sku_runtime')
            ->whereIn('appRuntimeId', $appRuntimeIds)
            ->group('appSkuId')
            ->column('appSkuId');

        if (empty($appSkuIds)) {
            return AppSku::where('1=0'); // 返回空集合
        }

        return AppSku::whereIn('appSkuId', $appSkuIds);
    }

    /**
     * 搜索器：按标题搜索
     */
    public function searchTitleAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('title', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按应用代码搜索
     */
    public function searchAppCodeAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('appCode', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按上线状态搜索
     */
    public function searchIsOnlineAttr($query, $value)
    {
        if ($value !== null && $value !== '') {
            $query->where('isOnline', $value);
        }
    }

    /**
     * 搜索器：按私有状态搜索
     */
    public function searchIsPrivateAttr($query, $value)
    {
        if ($value !== null && $value !== '') {
            $query->where('isPrivate', $value);
        }
    }
}
