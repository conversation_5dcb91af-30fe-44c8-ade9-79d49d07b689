<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\View;
/**
 * 全局鉴权中间件
 * 模拟当前用户ID为1
 */
class Auth
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 模拟当前用户ID为1
        $request->userId = 1;//$_SERVER["HTTP_YM_USERID"];
        View::assign([
            'userId' => $request->userId,
        ]);
        return $next($request);
    }
}
