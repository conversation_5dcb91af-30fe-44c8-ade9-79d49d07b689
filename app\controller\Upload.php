<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\Filesystem;
use think\facade\Config;
use think\Request;
use think\exception\ValidateException;

/**
 * 上传控制器
 */
class Upload extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\Auth',
    ];

    /**
     * 上传图片
     */
    public function image(Request $request)
    {
        // 获取上传文件
        $file = $request->file('file');

        // 如果没有上传文件，返回错误
        if (!$file) {
            return json(['code' => 0, 'msg' => '请选择要上传的图片']);
        }

        // 验证上传文件
        try {
            validate([
                'file' => [
                    // 限制文件大小(单位b)，这里限制为5M
                    'fileSize' => 5 * 1024 * 1024,
                    // 限制文件类型
                    'fileExt'  => 'jpg,jpeg,png,gif,webp',
                    // 限制文件mime类型
                    'fileMime' => 'image/jpeg,image/png,image/gif,image/webp',
                ]
            ])->check(['file' => $file]);

            // 使用public磁盘存储，并获取上传后的路径
            $savename = Filesystem::disk('public')->putFile('images', $file);

            // 获取文件访问路径
            $url = '/storage/' . $savename;

            // 返回成功结果
            return json([
                'code' => 1,
                'msg' => '上传成功',
                'data' => [
                    'url' => $url,
                    'name' => $file->getOriginalName(),
                    'size' => $file->getSize(),
                    'mime' => $file->getMime(),
                ]
            ]);

        } catch (ValidateException $e) {
            // 验证失败，返回错误信息
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        } catch (\Exception $e) {
            // 其他错误
            return json(['code' => 0, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取外部图片
     */
    public function fetch(Request $request)
    {
        // 获取图片URL
        $url = $request->post('url');

        if (empty($url)) {
            return json(['code' => 0, 'msg' => '请提供图片URL']);
        }

        try {
            // 获取图片内容
            $content = file_get_contents($url);

            if ($content === false) {
                return json(['code' => 0, 'msg' => '无法获取图片']);
            }

            // 获取图片信息
            $info = pathinfo($url);
            $extension = isset($info['extension']) ? strtolower($info['extension']) : '';

            // 检查扩展名
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            if (!in_array($extension, $allowedExtensions)) {
                $extension = 'jpg'; // 默认扩展名
            }

            // 生成文件名
            $filename = md5($url . time()) . '.' . $extension;
            $savePath = public_path() . 'storage/images/' . $filename;

            // 确保目录存在
            $dir = dirname($savePath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            // 保存文件
            file_put_contents($savePath, $content);

            // 返回URL
            $fileUrl = '/storage/images/' . $filename;

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'url' => $fileUrl,
                    'originalURL' => $url
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取图片失败：' . $e->getMessage()]);
        }
    }
}
