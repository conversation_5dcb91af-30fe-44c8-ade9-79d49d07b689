{extend name="common/base" /}

{block name="title"}{$app.title} - 应用详情 - 应用市场{/block}

{block name="style"}
<!-- 引入YmService中的图标CSS -->
<link rel="stylesheet" href="{$ymConfig.icon ?? '{__MSTATIC__}/css/iconfont.css'}">
<!-- 引入插件图标CSS -->
<link rel="stylesheet" href="{$ymPlugins.icon_css ?? '{__MSTATIC__}/css/iconfont.css'}">
<!-- 引入Vditor编辑器样式 -->
<link rel="stylesheet" href="{__MSTATIC__}/css/vditor.css">
<!-- 引入GitHub风格的Markdown样式 -->
<style>
    /* Markdown内容样式 */
    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        max-width: 100%;
        margin: 0 auto;
        padding: 15px;
    }

    /* 大纲样式 */
    .vditor-outline {
        position: relative;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        padding: 10px;
        margin-bottom: 15px;
        background-color: #f9fafb;
    }

    .vditor-outline__title {
        font-weight: 600;
        color: #4f46e5;
        margin-bottom: 8px;
    }

    .vditor-outline__content {
        max-height: 200px;
        overflow-y: auto;
    }

    /* 代码高亮样式 */
    .vditor-reset pre {
        background-color: #f6f8fa;
        border-radius: 6px;
        padding: 16px;
        overflow: auto;
    }

    /* 表格样式 */
    .vditor-reset table {
        border-collapse: collapse;
        width: 100%;
        margin: 16px 0;
    }

    .vditor-reset table th,
    .vditor-reset table td {
        border: 1px solid #e5e7eb;
        padding: 8px 12px;
    }

    .vditor-reset table th {
        background-color: #f3f4f6;
        font-weight: 600;
    }

    /* 引用样式 */
    .vditor-reset blockquote {
        border-left: 4px solid #e5e7eb;
        padding-left: 16px;
        color: #6b7280;
        margin: 16px 0;
    }
</style>
<style>
    /* 卡片悬停效果 */
    .runtime-card {
        transition: all 0.3s ease;
    }
    .runtime-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    /* 标签动画 */
    .app-tag {
        transition: all 0.2s ease;
    }
    .app-tag:hover {
        transform: scale(1.05);
    }
    /* 图片加载动画 */
    @keyframes imgLoading {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    .app-logo {
        animation: imgLoading 0.5s ease-in-out;
    }
    /* 响应式调整 */
    @media (max-width: 640px) {
        .app-description {
            -webkit-line-clamp: 4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }
    /* 渐变背景 */
    .bg-gradient-header {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    }
    /* SKU卡片样式 */
    .sku-card {
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }
    .sku-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #4f46e5;
    }
    .price-tag {
        transition: all 0.2s ease;
    }
    .sku-card:hover .price-tag {
        transform: scale(1.05);
    }
    /* 购买按钮 */
    .buy-button {
        transition: all 0.3s ease;
    }
    .buy-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    /* 选项卡样式 */
    .detail-tab {
        transition: all 0.2s ease;
        cursor: pointer;
        border-bottom: 2px solid transparent;
    }
    .detail-tab.active {
        border-color: #4f46e5;
        color: #4f46e5;
        font-weight: 600;
    }
    .detail-tab:hover:not(.active) {
        border-color: #a5b4fc;
        color: #6366f1;
    }

    /* 内容区域样式 */
    .tab-content {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }
    .tab-content.hidden {
        display: none;
    }

    /* 运行时选项卡样式 */
    .runtime-tab {
        transition: all 0.2s ease;
        cursor: pointer;
    }
    .runtime-tab.active {
        border-color: #4f46e5;
        color: #4f46e5;
        font-weight: 500;
    }
    .runtime-tab:hover:not(.active) {
        border-color: #a5b4fc;
        color: #6366f1;
    }

    /* 运行时内容区域样式 */
    .runtime-tab-content {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }
    .runtime-tab-content.hidden {
        display: none;
    }

    /* 环境卡片选中状态 */
    .runtime-card.border-indigo-500 {
        background-color: #eef2ff;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* 插件卡片样式 */
    #runtime-plugins-container .grid {
        max-height: 300px;
        overflow-y: auto;
        padding-right: 5px;
    }
    #runtime-plugins-container .grid::-webkit-scrollbar {
        width: 4px;
    }
    #runtime-plugins-container .grid::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    #runtime-plugins-container .grid::-webkit-scrollbar-thumb {
        background: #c7d2fe;
        border-radius: 10px;
    }
    #runtime-plugins-container .grid::-webkit-scrollbar-thumb:hover {
        background: #a5b4fc;
    }

    /* 环境详情区域动画 */
    #runtime-detail {
        transition: all 0.3s ease;
    }
    #runtime-detail:not(.hidden) {
        animation: fadeIn 0.5s ease-in-out;
    }
    @keyframes fadeIn {
        0% { opacity: 0; transform: translateY(-10px); }
        100% { opacity: 1; transform: translateY(0); }
    }
    /* 评价星星 */
    .star-rating {
        color: #f59e0b;
    }
    /* 推荐标签 */
    .recommend-badge {
        position: absolute;
        top: -10px;
        right: 10px;
        background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        font-weight: bold;
        transform: rotate(5deg);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 10;
    }
    /* 数量选择器 */
    .quantity-selector {
        display: flex;
        align-items: center;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    .quantity-selector button {
        width: 2rem;
        height: 2rem;
        background-color: #f3f4f6;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
    }
    .quantity-selector button:hover {
        background-color: #e5e7eb;
    }
    .quantity-selector input {
        width: 3rem;
        height: 2rem;
        border: none;
        text-align: center;
        font-size: 0.875rem;
    }
    .quantity-selector input:focus {
        outline: none;
    }
</style>
{/block}

{block name="content"}
{php}
// 预处理数据，准备传递给JavaScript
$runtimesDataJson = json_encode($runtimes);
$tagsJson = json_encode($ymPlugins['tags']);
$pluginsJson = json_encode($ymPlugins['plugins']);

// 处理图标数据
$iconsJson = [];
foreach ($runtimeIcons as $id => $icon) {
    $iconsJson[$id] = $icon;
}
$runtimeIconsDataJson = json_encode($iconsJson);
{/php}

<div class="mb-6">
    <script>
    // 环境数据
    const runtimesData = {$runtimesDataJson|raw};

    // 插件数据
    const pluginsData = {
        tags: {$tagsJson|raw},
        plugins: {$pluginsJson|raw}
    };

    // 环境图标数据
    const runtimeIconsData = {$runtimeIconsDataJson|raw};

    // 数量选择器功能
    function decreaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
        }
    }

    function increaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 99) {
            quantityInput.value = currentValue + 1;
        }
    }

    // 选项卡切换功能
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('.detail-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有选项卡的active类
                tabs.forEach(t => t.classList.remove('active'));
                // 为当前点击的选项卡添加active类
                this.classList.add('active');
            });
        });

        // 环境卡片点击事件
        const runtimeCards = document.querySelectorAll('.runtime-card');
        runtimeCards.forEach(card => {
            card.addEventListener('click', function() {
                // 获取环境ID
                const runtimeId = this.getAttribute('data-runtime-id');
                const runtimeName = this.getAttribute('data-runtime-name');

                // 显示环境详情
                showRuntimeDetail(runtimeId, runtimeName);

                // 移除所有环境卡片的选中状态
                runtimeCards.forEach(c => c.classList.remove('border-indigo-500'));
                // 添加当前卡片的选中状态
                this.classList.add('border-indigo-500');

                // 滚动到环境详情区域
                document.getElementById('runtime-detail').scrollIntoView({ behavior: 'smooth' });
            });
        });
    });



    // 显示环境详情
    function showRuntimeDetail(runtimeId, runtimeName) {
        // 获取环境详情区域
        const detailContainer = document.getElementById('runtime-detail');

        // 显示环境详情区域
        detailContainer.classList.remove('hidden');

        // 更新环境名称
        document.getElementById('runtime-detail-name').textContent = runtimeName;

        // 更新环境ID
        document.getElementById('runtime-detail-id').textContent = runtimeId;

        // 获取当前环境的详细信息
        const runtimeDetails = runtimesData.find(r => r.runtimeId == runtimeId);

        // 更新环境图标
        const iconElement = document.getElementById('runtime-detail-icon');
        if (runtimeIconsData[runtimeId]) {
            iconElement.className = `iconfont ${runtimeIconsData[runtimeId]} text-2xl`;
        } else {
            iconElement.className = 'iconfont icon-runtime text-2xl';
        }

        // 更新环境状态
        document.getElementById('runtime-detail-status').textContent =
            runtimeDetails.isOnline ? '已上线' : '未上线';

        // 更新环境内容
        const vditorViewElement = document.getElementById('vditor-view');
        if(runtimeDetails.content) {
            try {
                // 确保Vditor已加载
                if (typeof Vditor !== 'undefined') {
                    // 清空容器内容
                    vditorViewElement.innerHTML = '';

                    // 使用Vditor.preview渲染Markdown内容
                    Vditor.preview(vditorViewElement, runtimeDetails.content, {
                        mode: 'light',
                        hljs: {
                            lineNumber: true,
                            style: 'github'
                        },
                        markdown: {
                            toc: true,
                            sanitize: true,
                            linkBase: ''
                        },
                        after() {
                            // 渲染完成后的回调
                            console.log('Markdown渲染完成');

                            // 如果内容包含标题，生成大纲
                            if (vditorViewElement.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0) {
                                const outlineElement = document.createElement('div');
                                outlineElement.className = 'vditor-outline';
                                vditorViewElement.parentNode.insertBefore(outlineElement, vditorViewElement);

                                // 使用Vditor.outlineRender生成大纲
                                Vditor.outlineRender(vditorViewElement, outlineElement);
                            }
                        }
                    });
                } else {
                    vditorViewElement.innerHTML = '<p class="text-gray-500">Vditor编辑器未加载，无法渲染Markdown内容</p>';
                    console.error('Vditor is not defined. Make sure the script is loaded correctly.');
                }
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                vditorViewElement.innerHTML = '<p class="text-gray-500">Markdown渲染出错，请刷新页面重试</p>';
            }
        } else {
            vditorViewElement.innerHTML = '<p class="text-gray-500">暂无详细内容</p>';
        }

        // 更新演示链接
        const demoLinkContainer = document.getElementById('runtime-detail-demo-link');
        if(runtimeDetails.demoLink) {
            demoLinkContainer.innerHTML = `<a href="${runtimeDetails.demoLink}" target="_blank" class="text-indigo-600 hover:text-indigo-800">${runtimeDetails.demoLink}</a>`;
        } else {
            demoLinkContainer.innerHTML = '<span class="text-gray-500">暂无演示链接</span>';
        }

        // 更新插件列表
        updatePluginsList(runtimeDetails);
    }

    // 更新插件列表
    function updatePluginsList(runtimeDetails) {
        const pluginsContainer = document.getElementById('runtime-plugins-container');
        const pluginCountElement = document.getElementById('plugin-count');

        // 如果没有插件数据
        if (!runtimeDetails.plugins || runtimeDetails.plugins.length === 0) {
            pluginsContainer.innerHTML = '<p class="text-gray-500 text-sm">该环境无需额外插件</p>';
            if (pluginCountElement) {
                pluginCountElement.textContent = '0';
            }
            return;
        }

        // 筛选出必需的插件
        const requiredPlugins = runtimeDetails.plugins.filter(plugin => plugin.isRequired);

        // 如果没有必需的插件
        if (requiredPlugins.length === 0) {
            pluginsContainer.innerHTML = '<p class="text-gray-500 text-sm">该环境无需必需插件</p>';
            if (pluginCountElement) {
                pluginCountElement.textContent = '0';
            }
            return;
        }

        // 构建插件列表HTML
        let pluginsHtml = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">';

        // 遍历必需的插件列表
        requiredPlugins.forEach(plugin => {
            // 获取插件标签
            let tagNames = [];
            if (plugin.tag_ids && plugin.tag_ids.length > 0) {
                plugin.tag_ids.forEach(tagId => {
                    // 遍历所有标签组
                    pluginsData.tags.forEach(tagGroup => {
                        // 在标签组中查找匹配的标签
                        const tag = tagGroup.find(t => t.id === tagId);
                        if (tag) {
                            tagNames.push(tag.name);
                        }
                    });
                });
            }

            // 构建单个插件卡片
            pluginsHtml += `
                <div class="bg-white rounded-lg p-3 border border-indigo-300 hover:border-indigo-500 transition-all duration-200">
                    <div class="flex items-start mb-2">
                        <div class="flex-shrink-0 h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-2">
                            <i class="iconfont ${plugin.icon || 'icon-plugin'} text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <h5 class="font-medium text-gray-800 text-sm">${plugin.name} <span class="text-xs text-gray-500">(ID: ${plugin.id})</span></h5>
                            <div class="flex flex-wrap gap-1 mt-1">
                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">必需</span>
                                ${tagNames.map(tag => `<span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700">${tag}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                    ${plugin.description ? `<p class="text-xs text-gray-600 mt-1">${plugin.description}</p>` : ''}
                </div>
            `;
        });

        pluginsHtml += '</div>';

        // 更新插件容器内容
        pluginsContainer.innerHTML = pluginsHtml;

        // 更新插件计数
        if (pluginCountElement) {
            pluginCountElement.textContent = requiredPlugins.length;
        }
    }
    </script>
    <!-- 返回按钮 -->
    <div class="mb-4">
        <a href="{__MPRE__}{:url('market/index')}" class="inline-flex items-center px-4 py-2 bg-white rounded-lg shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <i class="iconfont icon-back mr-2"></i>
            返回应用市场
        </a>
    </div>

    <!-- 应用详情卡片 -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-6">
        <div class="bg-gradient-header text-white p-6 relative">
            <!-- 推荐标签 -->
            <div class="recommend-badge">
                <i class="iconfont icon-fire mr-1"></i> 热门推荐
            </div>

            <div class="flex flex-col md:flex-row items-start md:items-center">
                <img src="{$app.logo}" alt="{$app.title}" class="w-24 h-24 rounded-lg shadow-md object-cover mb-4 md:mb-0 md:mr-6 app-logo">
                <div class="flex-1">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold mb-2">{$app.title}</h1>
                            <p class="text-indigo-100 mb-3">AppCode: {$app.appCode}</p>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white app-tag">
                                    <i class="iconfont icon-online mr-1"></i>
                                    已上线
                                </span>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white app-tag">
                                    <i class="iconfont {$app.isPrivate ? 'icon-lock' : 'icon-app'} mr-1"></i>
                                    {$app.isPrivate ? '私有应用' : '公有应用'}
                                </span>
                            </div>
                        </div>

                        <div class="mt-4 md:mt-0 bg-white bg-opacity-10 p-3 rounded-lg">
                            <div class="flex items-center mb-2">
                                <div class="star-rating flex mr-2">
                                    {php}
                                        $rating = 4.8; // 模拟评分
                                    {/php}
                                    {for start="1" end="6"}
                                        <i class="iconfont {$i <= floor($rating) ? 'icon-star-fill' : ($i-0.5 <= $rating ? 'icon-star-half' : 'icon-star')} mr-0.5"></i>
                                    {/for}
                                </div>
                                <span class="text-white font-medium">{$rating}</span>
                            </div>
                            <div class="text-indigo-100 text-sm">
                                <span class="mr-3"><i class="iconfont icon-download mr-1"></i> 下载: 1.2k+</span>
                                <span><i class="iconfont icon-user mr-1"></i> 用户: 800+</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 购买选项部分 - 始终显示 -->
        <div class="p-6">
            <div class="mb-8">
                {if $skuCount > 0}
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-lg font-semibold text-gray-800">购买选项 ({$skuCount})</h2>
                    <span class="text-sm text-gray-500">选择合适的套餐</span>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {volist name="skus" id="sku"}
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden sku-card relative hover:shadow-md transition-all duration-300">
                        {php}
                            $isRecommend = rand(0, 1); // 随机决定是否显示推荐标签
                        {/php}
                        {if $isRecommend}
                        <div class="absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1 font-bold">推荐</div>
                        {/if}

                        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-2"></div>
                        <div class="p-4">
                            <div class="flex justify-between items-start mb-3">
                                <h3 class="text-lg font-semibold text-gray-800">{$sku.name}</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="iconfont icon-sku mr-1"></i> SKU
                                </span>
                            </div>

                            <!-- 运行时环境图标 -->
                            {if !empty($sku.runtimeIds)}
                            <div class="mb-3">
                                <div class="text-xs text-gray-500 mb-1">包含环境：</div>
                                <div class="flex flex-wrap gap-2">
                                    {volist name="sku.runtimeIds" id="runtimeId"}
                                    <div class="flex items-center bg-indigo-50 px-2 py-1 rounded-full" title="{$runtimeNames[$runtimeId] ?? '未知环境'}">
                                        <div class="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-1">
                                            <i class="iconfont {$runtimeIcons[$runtimeId] ?? 'icon-runtime'} text-sm"></i>
                                        </div>
                                        <span class="text-xs text-indigo-700">{$runtimeNames[$runtimeId] ?? '未知环境'}</span>
                                    </div>
                                    {/volist}
                                </div>
                            </div>
                            {/if}

                            {if $sku.description}
                            <p class="text-gray-600 text-sm mb-4">{$sku.description}</p>
                            {/if}

                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center price-tag">
                                    <span class="text-xl font-bold text-red-600">¥{$sku.salePrice}</span>
                                    {if $sku.fullPrice > $sku.salePrice}
                                    <span class="ml-2 text-sm text-gray-500 line-through">¥{$sku.fullPrice}</span>
                                    <span class="ml-2 text-xs px-1.5 py-0.5 bg-red-100 text-red-800 rounded">
                                        {php}echo round(($sku['salePrice']/$sku['fullPrice'])*10, 1);{/php}折
                                    </span>
                                    {/if}
                                </div>
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <i class="iconfont icon-package text-gray-500 mr-1"></i>
                                    <span class="text-sm text-gray-600">付费: {$sku.paidCount}</span>
                                </div>
                            </div>

                            <button class="w-full py-2 bg-indigo-600 text-white rounded-md font-medium hover:bg-indigo-700 transition-colors duration-200 flex items-center justify-center buy-button">
                                <i class="iconfont icon-cart mr-2"></i>
                                立即购买
                            </button>
                        </div>
                    </div>
                    {/volist}
                </div>
                {else}
                <div class="bg-gray-50 rounded-lg p-6 text-center mb-6">
                    <div class="text-gray-400 mb-3">
                        <i class="iconfont icon-empty text-5xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">暂无购买选项</h3>
                    <p class="text-gray-500">该应用暂未提供购买选项，请稍后再来查看</p>
                </div>
                {/if}

            </div>

        </div>

        <div class="p-6">
            <!-- 选项卡导航 -->
            <div class="flex border-b border-gray-200 mb-6 overflow-x-auto sticky top-0 bg-white z-10 py-2">
                <div class="detail-tab active px-4 py-2 font-medium" data-target="app-intro">应用介绍</div>
                <div class="detail-tab px-4 py-2 font-medium" data-target="app-environments">支持环境</div>
                <!-- <div class="detail-tab px-4 py-2 font-medium" data-target="app-reviews">用户评价</div> -->
            </div>

            <!-- 快速购买区域 -->
            <!-- {if $skuCount > 0}
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 mb-6 border border-indigo-100">
                <div class="flex flex-col md:flex-row items-start md:items-center justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-lg font-semibold text-gray-800 mb-1">立即购买</h3>
                        <p class="text-gray-600 text-sm">选择合适的套餐，享受优质服务</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <span class="text-gray-700 mr-2">数量:</span>
                            <div class="quantity-selector">
                                <button type="button" onclick="decreaseQuantity()">-</button>
                                <input type="number" id="quantity" value="1" min="1" max="99">
                                <button type="button" onclick="increaseQuantity()">+</button>
                            </div>
                        </div>
                        <button class="px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md font-medium hover:from-indigo-700 hover:to-purple-700 transition-colors duration-200 shadow-md buy-button">
                            <i class="iconfont icon-cart mr-2"></i>
                            立即购买
                        </button>
                    </div>
                </div>
            </div>
            {/if} -->

            <!-- 应用介绍部分 -->





            <div id="app-intro" class="tab-content active">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">应用介绍</h2>
                <div class="text-gray-600 mb-6 app-description bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                    {$app.description}
                </div>
            </div>
            <!-- 支持的环境部分 -->
            <div id="app-environments" class="tab-content hidden">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-lg font-semibold text-gray-800">支持的环境 ({$runtimeCount})</h2>
                    <span class="text-sm text-gray-500">点击卡片查看详情</span>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {volist name="runtimes" id="runtime"}
                    <div class="bg-white rounded-lg p-4 runtime-card border border-gray-100 hover:border-indigo-300 hover:shadow-md cursor-pointer transition-all duration-200"
                         data-runtime-id="{$runtime.runtimeId}"
                         data-runtime-name="{$runtimeNames[$runtime.runtimeId] ?? '未知环境'}">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                                <i class="iconfont {$runtimeIcons[$runtime.runtimeId] ?? 'icon-runtime'} text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-800">{$runtimeNames[$runtime.runtimeId] ?? '未知环境'}</h3>
                                <p class="text-xs text-gray-500">
                                    RuntimeId: {$runtime.runtimeId} | {$runtime.isOnline ? '已上线' : '未上线'}
                                </p>
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-indigo-600 text-right">
                            点击查看详情 <i class="iconfont icon-right"></i>
                        </div>
                    </div>
                    {/volist}
                </div>
            </div>

            <!-- 环境详情区域 -->
            <div id="runtime-detail" class="bg-white border border-indigo-200 rounded-lg shadow-md p-0 mb-6 hidden overflow-hidden">
                <!-- 环境详情头部 -->
                <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-12 w-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white mr-3">
                                <i class="iconfont" id="runtime-detail-icon">icon-runtime</i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white" id="runtime-detail-name">环境名称</h3>
                                <div class="flex items-center text-indigo-100">
                                    <span class="text-sm mr-3">RuntimeId: <span id="runtime-detail-id">0</span></span>
                                    <span class="text-sm">状态: <span id="runtime-detail-status">未知</span></span>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="px-4 py-2 bg-white text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors duration-200 buy-button">
                            <i class="iconfont icon-cart mr-1"></i> 购买此环境
                        </button>
                    </div>
                </div>

                <!-- 环境详情内容 -->
                <div class="p-5">
                    <!-- 环境详情选项卡 -->
                    <div class="border-b border-gray-200 mb-4">
                        <div class="flex space-x-4">
                            <button class="runtime-tab active px-3 py-2 text-sm font-medium border-b-2 border-indigo-500 text-indigo-600" data-tab="detail">详情内容</button>
                            <button class="runtime-tab px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="plugins">必需插件</button>
                            <button class="runtime-tab px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="demo">演示链接</button>
                            <button class="runtime-tab px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="sku">适用SKU</button>
                        </div>
                    </div>

                    <!-- 详情内容选项卡 -->
                    <div id="tab-detail" class="runtime-tab-content active">
                        <div id="runtime-detail-content-container" class="text-gray-600 text-sm">
                            <!-- Vditor渲染容器 -->
                            <div id="vditor-view" class="vditor-reset markdown-body"></div>
                            <!-- 大纲将在需要时动态插入到这里 -->
                        </div>
                    </div>

                    <!-- 插件选项卡 -->
                    <div id="tab-plugins" class="runtime-tab-content hidden">
                        <div class="mb-2 flex justify-between items-center">
                            <h4 class="text-md font-medium text-gray-800">必需插件</h4>
                            <span class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">数量: <span id="plugin-count" class="font-medium text-indigo-600">0</span></span>
                        </div>
                        <div id="runtime-plugins-container" class="mb-4">
                            <!-- 插件列表将通过JavaScript动态填充 -->
                            <div class="text-gray-500 text-sm">加载中...</div>
                        </div>
                    </div>

                    <!-- 演示链接选项卡 -->
                    <div id="tab-demo" class="runtime-tab-content hidden">
                        <h4 class="text-md font-medium text-gray-800 mb-2">演示链接</h4>
                        <div id="runtime-detail-demo-link" class="text-sm p-3 bg-gray-50 rounded-lg">
                            <!-- 演示链接将通过JavaScript动态填充 -->
                            <div class="text-gray-500 text-sm">加载中...</div>
                        </div>
                    </div>

                    <!-- 适用SKU选项卡 -->
                    <div id="tab-sku" class="runtime-tab-content hidden">
                        <h4 class="text-md font-medium text-gray-800 mb-2">适用的SKU</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {volist name="skus" id="sku" offset="0" length="2"}
                            <div class="bg-white rounded-lg p-3 border border-gray-200 hover:border-indigo-300 transition-all duration-200 shadow-sm">
                                <div class="flex justify-between items-start mb-2">
                                    <h5 class="font-medium text-gray-800">{$sku.name}</h5>
                                    <span class="text-red-600 font-bold">¥{$sku.salePrice}</span>
                                </div>

                                <!-- 运行时环境图标 -->
                                {if !empty($sku.runtimeIds)}
                                <div class="mb-2">
                                    <div class="text-xs text-gray-500 mb-1">包含环境：</div>
                                    <div class="flex flex-wrap gap-1">
                                        {volist name="sku.runtimeIds" id="runtimeId"}
                                        <div class="flex items-center bg-indigo-50 px-1.5 py-0.5 rounded-full" title="{$runtimeNames[$runtimeId] ?? '未知环境'}">
                                            <div class="w-4 h-4 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-1">
                                                <i class="iconfont {$runtimeIcons[$runtimeId] ?? 'icon-runtime'} text-xs"></i>
                                            </div>
                                            <span class="text-xs text-indigo-700">{$runtimeNames[$runtimeId] ?? ''}</span>
                                        </div>
                                        {/volist}
                                    </div>
                                </div>
                                {/if}
                                {if $sku.description}
                                <p class="text-xs text-gray-600 mb-2">{$sku.description}</p>
                                {/if}
                                <button class="w-full py-1.5 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 transition-colors duration-200">
                                    <i class="iconfont icon-cart mr-1"></i> 立即购买
                                </button>
                            </div>
                            {/volist}
                        </div>
                    </div>
                </div>

                <!-- 关闭按钮 -->
                <button id="close-runtime-detail" class="absolute top-2 right-2 text-white hover:text-indigo-200 transition-colors duration-200">
                    <i class="iconfont icon-close text-lg"></i>
                </button>
            </div>



            <!-- 用户评价区域 -->
            <div id="app-reviews" class="tab-content hidden">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-lg font-semibold text-gray-800">用户评价</h2>
                    <div class="flex items-center bg-indigo-50 px-3 py-1 rounded-full">
                        <div class="star-rating flex mr-2">
                            {for start="1" end="6"}
                                <i class="iconfont icon-star-fill mr-0.5"></i>
                            {/for}
                        </div>
                        <span class="text-indigo-700 font-medium">4.9</span>
                        <span class="text-indigo-500 text-sm ml-2">(128条评价)</span>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-4 mb-6 border border-gray-200 shadow-sm">
                    <div class="flex justify-between items-center mb-4 border-b border-gray-100 pb-3">
                        <h3 class="text-md font-medium text-gray-700">最新评价</h3>
                        <a href="#" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                            查看全部 <i class="iconfont icon-right"></i>
                        </a>
                    </div>

                    <div class="space-y-4">
                        {php}
                            // 模拟3条用户评价
                            $reviews = [
                                [
                                    'user' => '用户A',
                                    'rating' => 5,
                                    'content' => '非常好用的应用，功能齐全，界面美观，强烈推荐！',
                                    'time' => '2023-05-15'
                                ],
                                [
                                    'user' => '用户B',
                                    'rating' => 4,
                                    'content' => '整体不错，有些小功能还可以优化，期待后续更新。',
                                    'time' => '2023-04-20'
                                ],
                                [
                                    'user' => '用户C',
                                    'rating' => 5,
                                    'content' => '客服响应速度快，问题解决及时，体验很好。',
                                    'time' => '2023-03-10'
                                ]
                            ];
                        {/php}

                        {volist name="reviews" id="review"}
                        <div class="border-b border-gray-100 pb-4 last:border-0 last:pb-0 hover:bg-gray-50 p-2 rounded-lg transition-colors duration-200">
                            <div class="flex justify-between items-start mb-2">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                                        <i class="iconfont icon-user"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-800">{$review.user}</div>
                                        <div class="flex items-center">
                                            <div class="star-rating flex mr-2">
                                                {for start="1" end="6"}
                                                    <i class="iconfont {$i <= $review.rating ? 'icon-star-fill' : 'icon-star'} text-xs mr-0.5"></i>
                                                {/for}
                                            </div>
                                            <span class="text-gray-500 text-xs">{$review.time}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm ml-13">{$review.content}</p>
                        </div>
                        {/volist}
                    </div>

                    <!-- 评价按钮 -->
                    <div class="mt-4 text-center">
                        <button class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors duration-200 text-sm">
                            <i class="iconfont icon-edit mr-1"></i> 我要评价
                        </button>
                    </div>
                </div>
            </div>

            <!-- 相关推荐 -->
            <h2 class="text-lg font-semibold text-gray-800 mb-3">相关推荐</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {php}
                    // 模拟4个相关推荐应用
                    $relatedApps = [];
                    for ($i = 0; $i < 4; $i++) {
                        $relatedApps[] = [
                            'title' => ['相关应用A', '推荐应用B', '类似应用C', '热门应用D'][$i],
                            'price' => rand(50, 200),
                            'rating' => rand(45, 50) / 10
                        ];
                    }
                {/php}

                {volist name="relatedApps" id="related"}
                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:border-indigo-300 transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-semibold text-gray-800">{$related.title}</h3>
                        </div>
                        <div class="flex items-center mb-3">
                            <span class="text-yellow-500 text-xs">
                                {for start="1" end="6"}
                                    <i class="iconfont {$i <= floor($related.rating) ? 'icon-star-fill' : ($i-0.5 <= $related.rating ? 'icon-star-half' : 'icon-star')}"></i>
                                {/for}
                            </span>
                            <span class="text-xs text-gray-500 ml-1">{$related.rating}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-red-600 font-bold">¥{$related.price}</span>
                            <a href="#" class="text-indigo-600 hover:text-indigo-800 text-xs font-medium">
                                查看详情
                            </a>
                        </div>
                    </div>
                </div>
                {/volist}
            </div>

            <!-- 其他信息 -->
            <div class="border-t border-gray-200 pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">创建时间</h3>
                        <p class="mt-1 text-sm text-gray-900">{$app.createTime}</p>
                    </div>
                    {if $app.extra}
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">额外信息</h3>
                        <p class="mt-1 text-sm text-gray-900">
                            {volist name="app.extra" id="extra" key="key"}
                            <span class="block">{$key}: {$extra}</span>
                            {/volist}
                        </p>
                    </div>
                    {/if}
                </div>
            </div>

            <!-- 底部促销信息 -->
            <!-- <div class="mt-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-4 text-white">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-lg font-bold mb-1">限时优惠</h3>
                        <p class="text-blue-100">现在购买即可享受9折优惠，优惠码: MARKET2023</p>
                    </div>
                    <button class="px-6 py-2 bg-white text-indigo-600 rounded-md font-medium hover:bg-indigo-50 transition-colors duration-200 shadow-md buy-button">
                        立即抢购
                    </button>
                </div>
            </div> -->
        </div>
    </div>
</div>
{/block}

{block name="script"}
<!-- 引入Vditor编辑器脚本 -->
<script src="{__MSTATIC__}/js/vditor.js"></script>
<script>
// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查Vditor是否已加载
    if (typeof Vditor === 'undefined') {
        console.error('Vditor未加载，尝试重新加载脚本');

        // 重新加载Vditor脚本
        const script = document.createElement('script');
        script.src = '{__MSTATIC__}/js/vditor.js';
        script.onload = function() {
            console.log('Vditor脚本已重新加载');

            // 如果当前有选中的环境，重新渲染内容
            const selectedCard = document.querySelector('.runtime-card.border-indigo-500');
            if (selectedCard) {
                const runtimeId = selectedCard.getAttribute('data-runtime-id');
                const runtimeName = selectedCard.getAttribute('data-runtime-name');
                showRuntimeDetail(runtimeId, runtimeName);
            }
        };
        script.onerror = function() {
            console.error('Vditor脚本加载失败');
            alert('Markdown渲染组件加载失败，部分内容可能无法正常显示');
        };
        document.head.appendChild(script);
    } else {
        console.log('Vditor已成功加载');
    }

    // 主选项卡切换
    const tabs = document.querySelectorAll('.detail-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有选项卡的活动状态
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前选项卡的活动状态
            this.classList.add('active');

            // 隐藏所有内容区域
            tabContents.forEach(content => content.classList.add('hidden'));

            // 显示当前选项卡对应的内容区域
            const targetId = this.getAttribute('data-target');
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }
        });
    });

    // 环境详情内部选项卡切换
    const runtimeTabs = document.querySelectorAll('.runtime-tab');
    const runtimeTabContents = document.querySelectorAll('.runtime-tab-content');

    runtimeTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有选项卡的活动状态
            runtimeTabs.forEach(t => t.classList.remove('active'));
            // 添加当前选项卡的活动状态
            this.classList.add('active');

            // 隐藏所有内容区域
            runtimeTabContents.forEach(content => content.classList.add('hidden'));

            // 显示当前选项卡对应的内容区域
            const targetId = 'tab-' + this.getAttribute('data-tab');
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }
        });
    });

    // 关闭环境详情
    const closeRuntimeDetailBtn = document.getElementById('close-runtime-detail');
    if (closeRuntimeDetailBtn) {
        closeRuntimeDetailBtn.addEventListener('click', function() {
            const runtimeDetail = document.getElementById('runtime-detail');
            if (runtimeDetail) {
                runtimeDetail.classList.add('hidden');
            }
        });
    }

    // 初始化环境卡片点击事件
    initRuntimeCards();
});
</script>
{/block}