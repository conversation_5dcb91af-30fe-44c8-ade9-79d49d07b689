/*
 Navicat Premium Data Transfer mysql 5.7

 Source Server         : market
 Source Server Type    : MySQL
 Source Server Version : 50740 (5.7.40)
 Source Host           : ************:3306
 Source Schema         : market

 Target Server Type    : MySQL
 Target Server Version : 50740 (5.7.40)
 File Encoding         : 65001

 Date: 21/04/2025 10:13:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app
-- ----------------------------
DROP TABLE IF EXISTS `app`;
CREATE TABLE `app`  (
  `appId` int(11) NOT NULL AUTO_INCREMENT,
  `appCode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用唯一标识',
  `salerUserId` int(11) NOT NULL COMMENT '所有者、发布者',
  `title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说明',
  `logo` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '徽标',
  `configSchema` json NULL COMMENT '应用级配置表单',
  `extra` json NULL COMMENT '扩展配置',
  `isPrivate` tinyint(1) NOT NULL COMMENT '私有/公有应用? 私有时不放入应用市场，仅 ownerUserId 自己可开通购买',
  `isOnline` tinyint(1) NOT NULL COMMENT '上线/下线?  所有 "上线" 需做必要的配置检查是否符合上线要求',
  `paidCount` int(11) NOT NULL DEFAULT 0 COMMENT '付费数',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态，0 编辑中, 1 审核中, 2 审核通过',
  `statusMessage` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态信息，如拒绝理由',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`appId`) USING BTREE,
  INDEX `AK_Key_2`(`appCode` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app
-- ----------------------------

-- ----------------------------
-- Table structure for app_release
-- ----------------------------
DROP TABLE IF EXISTS `app_release`;
CREATE TABLE `app_release`  (
  `appReleaseId` int(11) NOT NULL AUTO_INCREMENT,
  `appRuntimeId` int(11) NOT NULL,
  `appBuildId` int(11) NOT NULL,
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说明',
  `isOnline` tinyint(1) NOT NULL COMMENT '上线/下线? ',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`appReleaseId`) USING BTREE,
  INDEX `AK_Key_2`(`appRuntimeId` ASC, `appBuildId` ASC) USING BTREE,
  INDEX `FK_Reference_13`(`appBuildId` ASC) USING BTREE,
  CONSTRAINT `FK_Reference_13` FOREIGN KEY (`appBuildId`) REFERENCES `app_build` (`appBuildId`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Reference_2` FOREIGN KEY (`appRuntimeId`) REFERENCES `app_runtime` (`appRuntimeId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发布' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_release
-- ----------------------------

-- ----------------------------
-- Table structure for app_runtime
-- ----------------------------
DROP TABLE IF EXISTS `app_runtime`;
CREATE TABLE `app_runtime`  (
  `appRuntimeId` int(11) NOT NULL AUTO_INCREMENT,
  `appId` int(11) NOT NULL,
  `runtimeId` int(11) NOT NULL COMMENT '运行时环境',
  `requiredPluginIds` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所需原生插件id，逗号分割',
  `configSchema` json NULL COMMENT '环境级配置表单',
  `extra` json NULL COMMENT '扩展配置',
  `demoLink` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '演示链接',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情 HTML 编辑器',
  `isOnline` tinyint(1) NOT NULL COMMENT '上线/下线?',
  `paidCount` int(11) NOT NULL DEFAULT 0 COMMENT '付费数',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态，0 编辑中, 1 审核中, 2 审核通过',
  `statusMessage` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态信息，如拒绝理由',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`appRuntimeId`) USING BTREE,
  INDEX `AK_Key_2`(`appId` ASC, `runtimeId` ASC) USING BTREE,
  CONSTRAINT `FK_Reference_1` FOREIGN KEY (`appId`) REFERENCES `app` (`appId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '适配的运行环境 Android、iOS、Windows ...' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_runtime
-- ----------------------------

-- ----------------------------
-- Table structure for app_sku
-- ----------------------------
DROP TABLE IF EXISTS `app_sku`;
CREATE TABLE `app_sku`  (
  `appSkuId` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称，如 Android,  Android + iOS',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `salePrice` int(11) NOT NULL COMMENT '售价',
  `fullPrice` int(11) NOT NULL COMMENT '原价',
  `paidCount` int(11) NOT NULL DEFAULT 0 COMMENT '付费数',
  `sort` int(11) NOT NULL COMMENT '递升排序',
  `isOnline` tinyint(1) NOT NULL COMMENT '在售/停售，在 ym_app_paid_runtime.appSkuId 中存在的已售 sku 不允许删除，不然无法续费，不用了就设为下线。',
  PRIMARY KEY (`appSkuId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售卖' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_sku
-- ----------------------------

-- ----------------------------
-- Table structure for app_sku_runtime
-- ----------------------------
DROP TABLE IF EXISTS `app_sku_runtime`;
CREATE TABLE `app_sku_runtime`  (
  `appSkuId` int(11) NOT NULL,
  `appRuntimeId` int(11) NOT NULL,
  PRIMARY KEY (`appSkuId`, `appRuntimeId`) USING BTREE,
  INDEX `FK_Reference_5`(`appRuntimeId` ASC) USING BTREE,
  CONSTRAINT `FK_Reference_4` FOREIGN KEY (`appSkuId`) REFERENCES `app_sku` (`appSkuId`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Reference_5` FOREIGN KEY (`appRuntimeId`) REFERENCES `app_runtime` (`appRuntimeId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合套餐' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_sku_runtime
-- ----------------------------

-- ----------------------------
-- Table structure for app_build
-- ----------------------------
CREATE TABLE `app_build` (
  `appBuildId` int(11) NOT NULL AUTO_INCREMENT,
  `appId` int(11) NOT NULL COMMENT '所有者、发布者',
  `versionCode` int(11) NOT NULL COMMENT '版本号，必需大于上次发布的版本，添加后不可修改',
  `versionName` varchar(16) NOT NULL COMMENT '版本名',
  `description` varchar(512) NOT NULL COMMENT '说明',
  `configSchema` json DEFAULT NULL COMMENT '应用包级配置表单',
  `appConfig` json NOT NULL COMMENT '类似微信小程序 app.json, {entryPagePath: "", ...}',
  `pageConfig` json DEFAULT NULL COMMENT '类似微信小程序 page 设置 [ { page: "pathRegexPattern", config: { } }, ... ]',
  `hasPackage` tinyint(1) NOT NULL COMMENT '有离线前端包? 有就需要上传',
  `packageUploaded` tinyint(1) NOT NULL COMMENT '前端离线包已上传?',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态，0 编辑中, 1 审核中, 2 审核通过',
  `statusMessage` varchar(256) DEFAULT NULL COMMENT '状态信息，如拒绝理由',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`appBuildId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Records of app_build
-- ----------------------------


SET FOREIGN_KEY_CHECKS = 1;
