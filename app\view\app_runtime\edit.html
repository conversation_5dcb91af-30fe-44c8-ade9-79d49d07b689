{extend name="common/base" /}

{block name="title"}{$app.title} - {$runtimeName}环境 - 应用市场管理系统{/block}

{block name="style"}
{if isset($ymPlugins.icon_css)}
<link rel="stylesheet" href="{$ymPlugins.icon_css}">
{/if}
<!-- form-create设计器依赖 -->
<link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
<style>
.plugin-item {
    transition: all 0.3s ease;
    position: relative;
}
.plugin-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.plugin-item.selected {
    border-color: #4f46e5;
    background-color: #eef2ff;
}
.plugin-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f3f4f6;
}
.plugin-item.selected .plugin-icon {
    background-color: #e0e7ff;
    color: #4f46e5;
}
.plugin-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}
.plugin-item.selected .plugin-checkbox {
    background-color: #4f46e5;
    border-color: #4f46e5;
}
.plugin-item.selected .plugin-checkbox:after {
    content: '';
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    display: block;
    margin-top: -2px;
}
/* 表格滚动样式 */
.max-h-96 {
    max-height: 24rem;
}
/* 确保表头在滚动时保持固定 */
.sticky {
    position: sticky;
}
.top-0 {
    top: 0;
}
.z-10 {
    z-index: 10;
}
/* 表头背景色，确保滚动时不透明 */
thead.sticky {
    background-color: #f9fafb;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 表单美化样式 */
.form-group {
    position: relative;
    margin-bottom: 0.5rem;
}

.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 输入框动画 */
input, select, textarea {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 加载动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 错误提示动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
    animation: shake 0.6s ease-in-out;
}

/* 表格行悬停效果 */
.plugin-row:hover {
    background-color: #f9fafb;
}

/* 美化滚动条 */
.max-h-96::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.max-h-96::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

/* 表单设计器弹窗样式 */
.form-designer-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.form-designer-modal.active {
    opacity: 1;
    visibility: visible;
}

.form-designer-container {
    width: 90%;
    height: 90%;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-designer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.form-designer-body {
    flex: 1;
    overflow: auto;
    position: relative;
}

.form-designer-footer {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    background-color: #f9fafb;
}

/* 修复form-create设计器与tailwind的样式冲突 */
#form-designer-container [class^="el-"] {
    box-sizing: border-box;
}

#form-designer-container .el-button {
    margin: 0;
}
</style>
{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="iconfont {$runtimeIcon} text-indigo-600 mr-2"></i>
                    {$runtimeName}环境配置
                </h2>
                <div class="flex space-x-2">
                    <a href="{__MPRE__}{:url('app/detail', ['appId' => $app.appId], false, false)}" class="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs flex items-center">
                        <i class="iconfont icon-back mr-1"></i> 返回应用
                    </a>
                </div>
            </div>

            <!-- 选项卡 -->
            <div class="border-b border-gray-200 mb-6">
                <ul class="flex flex-wrap -mb-px">
                    <li class="mr-2">
                        <a href="{__MPRE__}{:url('appRuntime/edit', ['appId' => $app.appId, 'runtimeId' => $runtimeId, 'tab' => 'config'], false, false)}"
                           class="inline-flex items-center py-2 px-4 text-sm font-medium {$tab == 'config' ? 'text-indigo-600 border-b-2 border-indigo-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent'} transition-colors duration-200">
                            <i class="iconfont icon-setting mr-1"></i> 配置
                        </a>
                    </li>
                    {if $has}
                    <li class="mr-2">
                        <a href="{__MPRE__}{:url('appRelease/index', ['appId' => $app.appId, 'runtimeId' => $runtimeId], false, false)}"
                           class="inline-flex items-center py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent transition-colors duration-200">
                            <i class="iconfont icon-release mr-1"></i> 发布列表
                        </a>
                    </li>
                    {/if}
                </ul>
            </div>

            <form id="runtimeForm" data-validate>
                <!-- 基本配置 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-setting text-indigo-500 mr-2"></i>基本配置
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="demoLink" class="block text-xs font-medium text-gray-700 mb-1">演示链接</label>
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-link text-gray-400 text-xs"></i>
                                </div>
                                <input type="url" id="demoLink" name="demoLink" value="{$appRuntime.demoLink|default=''}"
                                    placeholder="https://example.com"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级配置 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-code text-indigo-500 mr-2"></i>高级配置
                    </h3>
                    <div class="grid grid-cols-1 gap-5">
                        <div class="form-group">
                            <label for="configSchema" class="block text-xs font-medium text-gray-700 mb-1">环境级配置表单 (JSON)</label>
                            <div class="relative">
                                <div class="absolute top-1.5 left-3 text-gray-400 text-xs">{ }</div>
                                <textarea id="configSchema" name="configSchema" rows="3"
                                    class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                    focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                    hover:border-indigo-300 transition-colors duration-200 font-mono text-xs">{if isset($appRuntime.configSchema)}{:json_encode($appRuntime.configSchema, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)}{/if}</textarea>
                                <button type="button" id="openFormDesigner"
                                    class="absolute right-2 top-1.5 px-2 py-1 text-xs bg-indigo-600 text-white rounded
                                    hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                    focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                                    <i class="iconfont icon-form mr-1"></i> 设计器
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="extra" class="block text-xs font-medium text-gray-700 mb-1">扩展配置 (JSON)</label>
                            <div class="relative">
                                <div class="absolute top-1.5 left-3 text-gray-400 text-xs">{ }</div>
                                <textarea id="extra" name="extra" rows="3"
                                    class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                    focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                    hover:border-indigo-300 transition-colors duration-200 font-mono text-xs">{if isset($appRuntime.extra)}{:json_encode($appRuntime.extra, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)}{/if}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详情内容 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-file-markdown text-indigo-500 mr-2"></i>详情内容
                    </h3>
                    <div class="form-group">
                        <label for="content" class="block text-xs font-medium text-gray-700 mb-1">详情内容 (Markdown)</label>
                        <!-- 隐藏的textarea用于存储编辑器内容 -->
                        <textarea id="content" name="content" style="display: none;">{$appRuntime.content|default=''}</textarea>
                        <!-- Vditor编辑器容器 -->
                        <div id="vditor-container" class="mt-1 rounded-md border border-gray-300 overflow-hidden"></div>
                        <p class="mt-1 text-xs text-gray-500">支持Markdown格式，可以插入图片、表格、代码等。可直接粘贴图片或拖拽图片到编辑器中上传。</p>
                    </div>
                </div>
                    <!-- 插件选择 -->
                    {if !empty($ymPlugins.plugins) && !empty($ymPlugins[$pluginKey]) &&  count($ymPlugins[$pluginKey]) > 0}
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                            <i class="iconfont icon-plugin text-indigo-500 mr-2"></i>插件配置
                        </h3>
                        <div class="form-group">
                            <div class="flex justify-between items-center mb-2">
                                <label class="block text-xs font-medium text-gray-700">所需原生插件</label>
                                <span class="text-xs text-indigo-600 font-medium flex items-center">
                                    <i class="iconfont icon-check mr-1"></i> 支持多选
                                </span>
                            </div>
                            <input type="hidden" id="requiredPluginIds" name="requiredPluginIds" value="{$appRuntime.requiredPluginIds|default=''}">

                            <!-- 搜索和筛选 -->
                            <div class="mb-4 flex flex-col md:flex-row gap-4">
                                <div class="flex-1">
                                    <label for="pluginSearch" class="block text-xs font-medium text-gray-700 mb-1">搜索插件</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="iconfont icon-search text-gray-400 text-xs"></i>
                                        </div>
                                        <input type="text" id="pluginSearch" placeholder="输入插件名称或描述..."
                                            class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                            hover:border-indigo-300 transition-colors duration-200">
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <label for="tagFilter" class="block text-xs font-medium text-gray-700 mb-1">按标签筛选</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="iconfont icon-tag text-gray-400 text-xs"></i>
                                        </div>
                                        <select id="tagFilter"
                                            class="w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                            hover:border-indigo-300 transition-colors duration-200">
                                            <option value="">全部标签</option>
                                            {if isset($ymPlugins.tags) && !empty($ymPlugins.tags[0])}
                                                {volist name="ymPlugins.tags[0]" id="tag"}
                                                <option value="{$tag.id}">{$tag.name}</option>
                                                {/volist}
                                            {/if}
                                        </select>
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 插件表格 -->
                            <div class="border rounded-lg overflow-hidden shadow-sm">
                                <div class="overflow-x-auto">
                                    <div class="max-h-96 overflow-y-auto">
                                        <table id="pluginsTable" class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50 sticky top-0 z-10">
                                                <tr>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选择</th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">插件</th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标签</th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                {volist name="ymPlugins.plugins" id="plugin"}
                                                    {if in_array($plugin.id,$ymPlugins[$pluginKey])}
                                                    <tr class="plugin-row hover:bg-gray-50 transition-colors duration-150" data-plugin-id="{$plugin.id}" data-plugin-tags="{:implode(',', $plugin.tag_ids)}">
                                                        <td class="px-3 py-3 whitespace-nowrap">
                                                            <div class="flex items-center justify-center">
                                                                <div class="h-5 w-5">
                                                                    <input type="checkbox" class="plugin-checkbox-input focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded cursor-pointer"
                                                                        data-plugin-id="{$plugin.id}"
                                                                        {if in_array($plugin.id, $selectedPluginIds)}checked{/if}
                                                                        onchange="togglePluginCheckbox(this)">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="px-3 py-3 whitespace-nowrap">
                                                            <div class="flex items-center">
                                                                <div class="plugin-icon mr-3 text-indigo-500">
                                                                    <i class="iconfont {$plugin.icon}"></i>
                                                                </div>
                                                                <div>
                                                                    <div class="text-sm font-medium text-gray-900">{$plugin.name}</div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="px-3 py-3">
                                                            <div class="text-xs text-gray-500 line-clamp-2">{$plugin.description}</div>
                                                        </td>
                                                        <td class="px-3 py-3 whitespace-nowrap">
                                                            <div class="text-xs font-medium text-indigo-600">¥{$plugin['adr_price']}</div>
                                                        </td>
                                                        <td class="px-3 py-3">
                                                            <div class="flex flex-wrap gap-1">
                                                                {if isset($plugin.tag_ids) && !empty($plugin.tag_ids)}
                                                                    {volist name="plugin.tag_ids" id="tagId"}
                                                                        {if isset($ymPlugins.tags) && !empty($ymPlugins.tags[0])}
                                                                            {foreach $ymPlugins.tags[0] as $tag}
                                                                                {if $tag.id == $tagId}
                                                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                                                                                    {$tag.name}
                                                                                </span>
                                                                                {/if}
                                                                            {/foreach}
                                                                        {/if}
                                                                    {/volist}
                                                                {else}
                                                                    <span class="text-xs text-gray-500">无标签</span>
                                                                {/if}
                                                            </div>
                                                        </td>
                                                        <td class="px-3 py-3 whitespace-nowrap text-xs text-gray-500">
                                                            {$plugin.id}
                                                        </td>
                                                    </tr>
                                                    {/if}
                                                {/volist}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-2 text-xs text-gray-500">点击复选框选择所需插件，已选择 <span id="selectedCount" class="font-medium text-indigo-600">0</span> 个</p>
                        </div>
                    </div>
                    {/if}

                    <!-- 状态设置 -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                            <i class="iconfont icon-status text-indigo-500 mr-2"></i>状态设置
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label class="block text-xs font-medium text-gray-700 mb-2">上线状态 <span class="text-red-500">*</span></label>
                                <div class="flex space-x-5 mt-1">
                                    <label class="inline-flex items-center cursor-pointer group">
                                        <div class="relative">
                                            <input type="radio" name="isOnline" value="0" {if !isset($appRuntime.isOnline) || $appRuntime.isOnline == 0}checked{/if}
                                                class="peer sr-only">
                                            <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                                group-hover:border-indigo-400 peer-checked:border-indigo-600
                                                peer-checked:border-3 transition-all duration-200"></div>
                                        </div>
                                        <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">未上线</span>
                                    </label>
                                    <label class="inline-flex items-center cursor-pointer group">
                                        <div class="relative">
                                            <input type="radio" name="isOnline" value="1" {if isset($appRuntime.isOnline) && $appRuntime.isOnline == 1}checked{/if}
                                                class="peer sr-only">
                                            <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                                group-hover:border-indigo-400 peer-checked:border-indigo-600
                                                peer-checked:border-3 transition-all duration-200"></div>
                                        </div>
                                        <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">已上线</span>
                                    </label>
                                </div>
                            </div>

                            <!-- <div class="form-group">
                                <label class="block text-xs font-medium text-gray-700 mb-1">状态 <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <select name="status" required
                                        class="appearance-none w-full pl-3 pr-8 py-1.5 text-sm border border-gray-300 rounded-md
                                        focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                        hover:border-indigo-300 transition-colors duration-200 bg-white">
                                        <option value="0" {if !isset($appRuntime.status) || $appRuntime.status == 0}selected{/if}>编辑中</option>
                                        <option value="1" {if isset($appRuntime.status) && $appRuntime.status == 1}selected{/if}>审核中</option>
                                        <option value="2" {if isset($appRuntime.status) && $appRuntime.status == 2}selected{/if}>审核通过</option>
                                    </select>
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                                        <i class="iconfont icon-down text-xs"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="statusMessage" class="block text-xs font-medium text-gray-700 mb-1">状态信息</label>
                                <div class="relative">
                                    <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                        <i class="iconfont icon-message text-xs"></i>
                                    </span>
                                    <input type="text" id="statusMessage" name="statusMessage" value="{$appRuntime.statusMessage|default=''}"
                                        placeholder="如拒绝理由等"
                                        class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                        focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                        hover:border-indigo-300 transition-colors duration-200">
                                </div>
                            </div>
                            -->
                        </div>
                    </div> 
                </div>

                <div class="mt-6 flex justify-end space-x-3 border-t border-gray-100 pt-5">
                    <button type="button" onclick="window.location.href='{__MPRE__}{:url(\'app/detail\', [\'appId\' => $app.appId])}'"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-close mr-1"></i> 取消
                    </button>
                    <button type="button" onclick="submitForm()"
                        class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-save mr-1"></i> 保存配置
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 表单设计器弹窗 -->
<div id="formDesignerModal" class="form-designer-modal">
    <div class="form-designer-container">
        <div class="form-designer-header">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="iconfont icon-form text-indigo-500 mr-2"></i>表单设计器
            </h3>
            <button type="button" id="closeFormDesigner" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                <i class="iconfont icon-close text-lg"></i>
            </button>
        </div>
        <div class="form-designer-body">
            <div id="form-designer-container"></div>
        </div>
        <div class="form-designer-footer">
            <button type="button" id="cancelFormDesign" class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                <i class="iconfont icon-close mr-1"></i> 取消
            </button>
            <button type="button" id="saveFormDesign" class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                <i class="iconfont icon-save mr-1"></i> 保存设计
            </button>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<!-- 引入Vditor编辑器脚本 -->
<script src="{__MSTATIC__}/js/vditor.js"></script>
<!-- 引入form-create设计器依赖 -->
<script src="https://unpkg.com/vue@3"></script>
<script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
<script src="https://unpkg.com/@form-create/element-ui@next/dist/form-create.min.js"></script>
<script src="https://unpkg.com/@form-create/designer@next/dist/index.umd.js"></script>
<script>
// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表单元素焦点效果
    initFormElements();

    // 初始化插件选择
    initPluginSelection();

    // 初始化表单设计器
    initFormDesigner();

    // 初始化Vditor编辑器
    // 注意：Vditor编辑器的初始化代码已经在页面加载时执行
});

// 初始化表单元素
function initFormElements() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], input[type="url"], textarea, select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');

            // 简单验证（如果是必填字段）
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('border-red-500');
            } else {
                this.classList.remove('border-red-500');
            }
        });
    });
}

// 初始化表单设计器
function initFormDesigner() {
    // 获取相关DOM元素
    const modal = document.getElementById('formDesignerModal');
    const openBtn = document.getElementById('openFormDesigner');
    const closeBtn = document.getElementById('closeFormDesigner');
    const cancelBtn = document.getElementById('cancelFormDesign');
    const saveBtn = document.getElementById('saveFormDesign');
    const configSchemaInput = document.getElementById('configSchema');
    const designerContainer = document.getElementById('form-designer-container');

    let designer = null;

    // 打开设计器
    openBtn.addEventListener('click', function() {
        modal.classList.add('active');

        // 初始化设计器
        if (!designer) {
            // 创建设计器实例
            const app = Vue.createApp({
                data() {
                    return {};
                },
                template: '<fc-designer ref="designer" />'
            });

            // 注册设计器组件
            app.use(ElementPlus);
            app.use(formCreate);
            app.use(FcDesigner);

            // 挂载设计器
            const vm = app.mount(designerContainer);
            designer = vm.$refs.designer;

            // 如果已有配置数据，则导入
            const existingConfig = configSchemaInput.value.trim();
            if (existingConfig) {
                try {
                    const configData = JSON.parse(existingConfig);
                    designer.setRule(configData);
                } catch (e) {
                    console.error('解析现有配置失败:', e);
                    showNotification('解析现有配置失败，将创建新表单', 'error');
                }
            }
        }
    });

    // 关闭设计器
    function closeDesigner() {
        modal.classList.remove('active');
    }

    closeBtn.addEventListener('click', closeDesigner);
    cancelBtn.addEventListener('click', closeDesigner);

    // 保存设计
    saveBtn.addEventListener('click', function() {
        if (designer) {
            try {
                // 获取设计器生成的规则
                const rule = designer.getRule();

                // 将规则转换为JSON字符串并格式化
                const jsonStr = JSON.stringify(rule, null, 2);

                // 更新输入框的值
                configSchemaInput.value = jsonStr;

                // 关闭设计器
                closeDesigner();

                // 显示成功通知
                showNotification('表单设计已保存', 'success');
            } catch (e) {
                console.error('保存表单设计失败:', e);
                showNotification('保存表单设计失败', 'error');
            }
        }
    });

    // 点击模态框背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeDesigner();
        }
    });
}

// 初始化插件选择
function initPluginSelection() {
    // 获取已选择的插件ID
    const requiredPluginIds = document.getElementById('requiredPluginIds')?.value;
    if (requiredPluginIds) {
        // 更新已选择的插件数量
        updateSelectedCount();

        // 检查选择状态的一致性
        validatePluginSelections();
    } else {
        // 如果没有已选择的插件，将计数器设为0
        const selectedCount = document.getElementById('selectedCount');
        if (selectedCount) {
            selectedCount.textContent = '0';
        }
    }

    // 初始化插件表格搜索和筛选功能
    const pluginSearch = document.getElementById('pluginSearch');
    const tagFilter = document.getElementById('tagFilter');

    if (pluginSearch && tagFilter) {
        // 搜索功能
        pluginSearch.addEventListener('input', filterPlugins);

        // 标签筛选功能
        tagFilter.addEventListener('change', filterPlugins);
    }
}

// 验证插件选择的一致性
function validatePluginSelections() {
    // 获取ID为0的组件复选框
    const basePluginCheckbox = document.querySelector('.plugin-checkbox-input[data-plugin-id="0"]');
    if (!basePluginCheckbox) {
        console.error('找不到ID为0的基础组件');
        return;
    }

    // 获取所有已选择的插件
    const selectedCheckboxes = document.querySelectorAll('.plugin-checkbox-input:checked');
    const hasNonBasePlugins = Array.from(selectedCheckboxes).some(cb => parseInt(cb.getAttribute('data-plugin-id')) > 0);

    // 如果有ID > 0的组件被选中，但ID为0的组件未被选中
    if (hasNonBasePlugins && !basePluginCheckbox.checked) {
        basePluginCheckbox.checked = true;
        console.log('已自动选中基础组件（ID为0），因为有其他组件被选中');
    }

    // 如果ID为0的组件未被选中，取消所有其他组件的选择
    if (!basePluginCheckbox.checked) {
        selectedCheckboxes.forEach(cb => {
            if (parseInt(cb.getAttribute('data-plugin-id')) > 0) {
                cb.checked = false;
            }
        });
    }

    // 更新隐藏字段和计数
    updatePluginIds();
    updateSelectedCount();
}

// 切换插件选择状态（复选框）
function togglePluginCheckbox(checkbox) {
    const pluginId = parseInt(checkbox.getAttribute('data-plugin-id'));
    const isChecked = checkbox.checked;

    // 获取ID为0的组件复选框
    const basePluginCheckbox = document.querySelector('.plugin-checkbox-input[data-plugin-id="0"]');

    if (!basePluginCheckbox) {
        console.error('找不到ID为0的基础组件');
        return;
    }

    // 如果选择的是ID为0的组件且取消选择
    if (pluginId === 0 && !isChecked) {
        // 取消所有已选择的组件
        document.querySelectorAll('.plugin-checkbox-input:checked').forEach(cb => {
            cb.checked = false;
        });

    }
    // 如果选择的是ID > 0的组件且选中
    else if (pluginId > 0 && isChecked) {
        // 确保ID为0的组件也被选中
        if (!basePluginCheckbox.checked) {
            basePluginCheckbox.checked = true;

        }
    }

    updatePluginIds();
    updateSelectedCount();
}

// 更新已选择的插件ID
function updatePluginIds() {
    const selectedCheckboxes = document.querySelectorAll('.plugin-checkbox-input:checked');
    const pluginIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.getAttribute('data-plugin-id'));
    document.getElementById('requiredPluginIds').value = pluginIds.join(',');
}

// 更新已选择的插件数量
function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.plugin-checkbox-input:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

// 筛选插件函数
function filterPlugins() {
    const searchTerm = document.getElementById('pluginSearch').value.toLowerCase();
    const selectedTag = document.getElementById('tagFilter').value;
    const pluginRows = document.querySelectorAll('#pluginsTable tbody tr.plugin-row');

    pluginRows.forEach(row => {
        const pluginName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const pluginDesc = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const pluginTags = row.getAttribute('data-plugin-tags') || '';
        const tagArray = pluginTags.split(',');

        // 检查是否匹配搜索词
        const matchesSearch = searchTerm === '' ||
            pluginName.includes(searchTerm) ||
            pluginDesc.includes(searchTerm);

        // 检查是否匹配选中的标签
        const matchesTag = selectedTag === '' || tagArray.includes(selectedTag);

        // 同时满足搜索和标签筛选条件才显示
        if (matchesSearch && matchesTag) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // 检查是否有可见行
    const visibleRows = document.querySelectorAll('#pluginsTable tbody tr.plugin-row:not([style*="display: none"])');
    const noResultsRow = document.getElementById('noResultsRow');

    // 如果没有可见行，显示"无匹配结果"
    if (visibleRows.length === 0) {
        if (!noResultsRow) {
            const tbody = document.querySelector('#pluginsTable tbody');
            const newRow = document.createElement('tr');
            newRow.id = 'noResultsRow';
            newRow.innerHTML = '<td colspan="6" class="px-3 py-4 text-center text-sm text-gray-500">没有匹配的插件</td>';
            tbody.appendChild(newRow);
        } else {
            noResultsRow.style.display = '';
        }
    } else if (noResultsRow) {
        noResultsRow.style.display = 'none';
    }
}

async function submitForm() {
    // 显示加载状态
    const saveButton = document.querySelector('button[onclick="submitForm()"]');
    const originalButtonText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="iconfont icon-loading animate-spin mr-1"></i> 保存中...';
    saveButton.disabled = true;

    try {
        // 先验证插件选择的一致性
        validatePluginSelections();

        // 检查是否有ID > 0的插件被选中但ID为0的插件未被选中
        const basePluginCheckbox = document.querySelector('.plugin-checkbox-input[data-plugin-id="0"]');
        const selectedCheckboxes = document.querySelectorAll('.plugin-checkbox-input:checked');
        const hasNonBasePlugins = Array.from(selectedCheckboxes).some(cb => parseInt(cb.getAttribute('data-plugin-id')) > 0);

        if (hasNonBasePlugins && (!basePluginCheckbox || !basePluginCheckbox.checked)) {
            showNotification('选择其他组件时，必须同时选择基础组件（ID为0）', 'error');
            resetButton();
            return;
        }

        // 确保从Vditor获取最新内容
        if (window.vditorEditor) {
            document.getElementById('content').value = window.vditorEditor.getValue();
        }

        const form = document.getElementById('runtimeForm');
        const formData = new FormData(form);

        // 处理JSON字段
        const configSchema = formData.get('configSchema');
        const extra = formData.get('extra');

        if (configSchema && configSchema.trim() !== '') {
            try {
                JSON.parse(configSchema);
            } catch (e) {
                showNotification('环境级配置表单不是有效的JSON格式', 'error');
                document.getElementById('configSchema').focus();
                resetButton();
                return;
            }
        }

        if (extra && extra.trim() !== '') {
            try {
                JSON.parse(extra);
            } catch (e) {
                showNotification('扩展配置不是有效的JSON格式', 'error');
                document.getElementById('extra').focus();
                resetButton();
                return;
            }
        }

        // 发送请求
        const response = await fetch('{__MPRE__}{:url("appRuntime/update")}?appId={$app.appId}&runtimeId={$runtimeId}', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.href = '{__MPRE__}{:url("appRuntime/edit")}?appId={$app.appId}&runtimeId={$runtimeId}';
            }, 1000);
        } else {
            showNotification(data.msg || '保存失败', 'error');
            resetButton();
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('保存失败，请重试', 'error');
        resetButton();
    }

    function resetButton() {
        saveButton.innerHTML = originalButtonText;
        saveButton.disabled = false;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>
{/block}
