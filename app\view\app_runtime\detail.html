{extend name="common/base" /}

{block name="title"}{$app.title} - {$runtimeName}环境详情 - 应用市场管理系统{/block}

{block name="style"}
<link rel="stylesheet" href="{__MSTATIC__}/css/bytemd.css">
{if isset($ymPlugins.icon_css)}
<link rel="stylesheet" href="{$ymPlugins.icon_css|default='{__MSTATIC__}/css/iconfont.css'}">
{/if}
<style>
.plugin-card {
    transition: all 0.3s ease;
}
.plugin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.plugin-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f3f4f6;
    color: #4f46e5;
}
/* 表格滚动样式 */
.max-h-96 {
    max-height: 24rem;
}
/* 确保表头在滚动时保持固定 */
.sticky {
    position: sticky;
}
.top-0 {
    top: 0;
}
.z-10 {
    z-index: 10;
}
/* 表头背景色，确保滚动时不透明 */
thead.sticky {
    background-color: #f9fafb;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
</style>
{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    {include file="common/sidebar" /}

    <!-- 主内容区 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <div class="flex items-center">
                    <i class="iconfont {$runtimeIcon} text-indigo-600 text-2xl mr-2"></i>
                    <h2 class="text-xl font-semibold text-gray-800">{$runtimeName}环境详情</h2>
                </div>
                <a href="{__MPRE__}{:url('appRuntime/edit', ['appId' => $app.appId, 'runtimeId' => $runtimeId], false, false)}" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="iconfont icon-edit"></i> 编辑环境
                </a>
            </div>

            <!-- 环境信息卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-3"></div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-700 mb-4">基本信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">环境类型</span>
                                    <span class="text-sm text-gray-800">{$runtimeName}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">上线状态</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$appRuntime.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                        <i class="iconfont {$appRuntime.isOnline ? 'icon-online' : 'icon-offline'} mr-1"></i>
                                        {$appRuntime.isOnline ? '已上线' : '未上线'}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">审核状态</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {switch name="appRuntime.status"}
                                            {case value="0"}bg-gray-100 text-gray-800{/case}
                                            {case value="1"}bg-yellow-100 text-yellow-800{/case}
                                            {case value="2"}bg-green-100 text-green-800{/case}
                                            {default /}bg-gray-100 text-gray-800
                                        {/switch}">
                                        {switch name="appRuntime.status"}
                                            {case value="0"}编辑中{/case}
                                            {case value="1"}审核中{/case}
                                            {case value="2"}审核通过{/case}
                                            {default /}未知状态
                                        {/switch}
                                    </span>
                                </div>
                                {if $appRuntime.demoLink}
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">演示链接</span>
                                    <a href="{$appRuntime.demoLink}" target="_blank" class="text-sm text-indigo-600 hover:text-indigo-800">
                                        <i class="iconfont icon-link"></i> 查看演示
                                    </a>
                                </div>
                                {/if}
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-gray-700 mb-4">配置信息</h3>
                            {if $appRuntime.configSchema}
                            <div class="mb-4">
                                <span class="text-sm font-medium text-gray-500">环境级配置表单</span>
                                <pre class="mt-1 bg-gray-50 p-3 rounded-md text-xs text-gray-700 overflow-auto max-h-32 border border-gray-200">{:json_encode($appRuntime.configSchema, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)}</pre>
                            </div>
                            {/if}

                            {if $appRuntime.extra}
                            <div>
                                <span class="text-sm font-medium text-gray-500">扩展配置</span>
                                <pre class="mt-1 bg-gray-50 p-3 rounded-md text-xs text-gray-700 overflow-auto max-h-32 border border-gray-200">{:json_encode($appRuntime.extra, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)}</pre>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 所需插件 -->
            {if !empty($selectedPlugins)}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-green-500 to-teal-500 h-3"></div>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">所需原生插件</h3>

                    <!-- 搜索和筛选 -->
                    <div class="mb-4 flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <label for="pluginSearch" class="block text-sm font-medium text-gray-700 mb-1">搜索插件</label>
                            <div class="relative">
                                <input type="text" id="pluginSearch" placeholder="输入插件名称或描述..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pl-10">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1">
                            <label for="tagFilter" class="block text-sm font-medium text-gray-700 mb-1">按标签筛选</label>
                            <select id="tagFilter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">全部标签</option>
                                {if isset($ymPlugins.tags) && !empty($ymPlugins.tags[0])}
                                    {volist name="ymPlugins.tags[0]" id="tag"}
                                    <option value="{$tag.id}">{$tag.name}</option>
                                    {/volist}
                                {/if}
                            </select>
                        </div>
                    </div>

                    <!-- 插件表格 -->
                    <div class="border rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <div class="max-h-96 overflow-y-auto">
                                <table id="pluginsTable" class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50 sticky top-0 z-10">
                                        <tr>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">插件</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标签</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {volist name="selectedPlugins" id="plugin"}
                                        <tr class="plugin-row" data-plugin-id="{$plugin.id}" data-plugin-tags="{:implode(',', $plugin.tag_ids)}">
                                            <td class="px-3 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="plugin-icon mr-3">
                                                        <i class="iconfont {$plugin.icon}"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{$plugin.name}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-3 py-4">
                                                <div class="text-sm text-gray-500 line-clamp-2">{$plugin.description}</div>
                                            </td>
                                            <td class="px-3 py-4 whitespace-nowrap">
                                                {if !empty($pluginKey) && isset($plugin[$pluginKey]) && $plugin[$pluginKey] > 0}
                                                <div class="text-sm font-medium text-indigo-600">¥{$plugin[$pluginKey]}</div>
                                                {else}
                                                <div class="text-sm text-gray-500">无价格</div>
                                                {/if}
                                            </td>
                                            <td class="px-3 py-4">
                                                <div class="flex flex-wrap gap-1">
                                                    {if isset($plugin.tag_ids) && !empty($plugin.tag_ids)}
                                                        {volist name="plugin.tag_ids" id="tagId"}
                                                            {if isset($ymPlugins.tags) && !empty($ymPlugins.tags[0])}
                                                                {foreach $ymPlugins.tags[0] as $tag}
                                                                    {if $tag.id == $tagId}
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                                                                        {$tag.name}
                                                                    </span>
                                                                    {/if}
                                                                {/foreach}
                                                            {/if}
                                                        {/volist}
                                                    {else}
                                                        <span class="text-xs text-gray-500">无标签</span>
                                                    {/if}
                                                </div>
                                            </td>
                                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {$plugin.id}
                                            </td>
                                        </tr>
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/if}

            <!-- 详情内容 -->
            {if $appRuntime.content}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-blue-500 to-cyan-500 h-3"></div>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">详情内容</h3>
                    <!-- Markdown渲染区域 -->
                    <div id="markdown-content" class="markdown-body"></div>
                </div>
            </div>
            {/if}

            <div class="mt-6 flex justify-end">
                <a href="{__MPRE__}{:url('app/detail', ['appId' => $app.appId], false, false)}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    返回应用详情
                </a>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<!-- 引入ByteMD渲染器 -->
<script src="https://cdn.jsdelivr.net/npm/bytemd/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-gfm/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-highlight/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-math/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-mermaid/dist/index.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Markdown渲染器
    const markdownContent = document.getElementById('markdown-content');
    if (markdownContent) {
        const { Viewer } = bytemd;
        const gfmPlugin = window.bytemdPluginGfm();
        const highlightPlugin = window.bytemdPluginHighlight();
        const mathPlugin = window.bytemdPluginMath();
        const mermaidPlugin = window.bytemdPluginMermaid();

        // 获取Markdown内容
        const content = `{$appRuntime.content|raw}`;

        // 创建Viewer实例
        new Viewer({
            target: markdownContent,
            props: {
                value: content,
                plugins: [
                    gfmPlugin,
                    highlightPlugin,
                    mathPlugin,
                    mermaidPlugin
                ]
            }
        });
    }

    // 初始化插件表格搜索和筛选功能
    const pluginSearch = document.getElementById('pluginSearch');
    const tagFilter = document.getElementById('tagFilter');

    if (pluginSearch && tagFilter) {
        // 搜索功能
        pluginSearch.addEventListener('input', filterPlugins);

        // 标签筛选功能
        tagFilter.addEventListener('change', filterPlugins);

        // 筛选插件函数
        function filterPlugins() {
            const searchTerm = pluginSearch.value.toLowerCase();
            const selectedTag = tagFilter.value;
            const pluginRows = document.querySelectorAll('#pluginsTable tbody tr');

            pluginRows.forEach(row => {
                const pluginName = row.querySelector('td:first-child').textContent.toLowerCase();
                const pluginDesc = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const pluginTags = row.getAttribute('data-plugin-tags') || '';
                const tagArray = pluginTags.split(',');

                // 检查是否匹配搜索词
                const matchesSearch = searchTerm === '' ||
                    pluginName.includes(searchTerm) ||
                    pluginDesc.includes(searchTerm);

                // 检查是否匹配选中的标签
                const matchesTag = selectedTag === '' || tagArray.includes(selectedTag);

                // 同时满足搜索和标签筛选条件才显示
                if (matchesSearch && matchesTag) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // 检查是否有可见行
            const visibleRows = document.querySelectorAll('#pluginsTable tbody tr:not([style*="display: none"])');
            const noResultsRow = document.getElementById('noResultsRow');

            // 如果没有可见行，显示"无匹配结果"
            if (visibleRows.length === 0) {
                if (!noResultsRow) {
                    const tbody = document.querySelector('#pluginsTable tbody');
                    const newRow = document.createElement('tr');
                    newRow.id = 'noResultsRow';
                    newRow.innerHTML = '<td colspan="5" class="px-3 py-4 text-center text-sm text-gray-500">没有匹配的插件</td>';
                    tbody.appendChild(newRow);
                } else {
                    noResultsRow.style.display = '';
                }
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        }
    }
});
</script>
{/block}
