<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * AppBuild模型
 */
class AppBuild extends Model
{
    // 设置表名
    protected $name = 'app_build';
    
    // 设置主键
    protected $pk = 'appBuildId';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 设置创建时间字段
    protected $createTime = 'createTime';
    
    // 设置更新时间字段
    protected $updateTime = false;
    
    // 设置JSON类型字段
    protected $json = ['configSchema', 'appConfig', 'pageConfig'];
    
    // 设置JSON数据返回数组
    protected $jsonAssoc = true;
    
    /**
     * 获取所属应用
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'appId', 'appId');
    }
    
    /**
     * 搜索器：按版本号搜索
     */
    public function searchVersionCodeAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('versionCode', $value);
        }
    }
    
    /**
     * 搜索器：按版本名称搜索
     */
    public function searchVersionNameAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('versionName', 'like', "%{$value}%");
        }
    }
}
