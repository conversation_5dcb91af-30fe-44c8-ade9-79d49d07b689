<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * AppRuntime模型
 */
class AppRuntime extends Model
{
    // 设置表名
    protected $name = 'app_runtime';

    // 设置主键
    protected $pk = 'appRuntimeId';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 设置创建时间字段
    protected $createTime = 'createTime';

    // 设置更新时间字段
    protected $updateTime = false;

    // 设置JSON类型字段
    protected $json = ['configSchema', 'extra'];

    // 设置JSON数据返回数组
    protected $jsonAssoc = true;

    /**
     * 获取所属应用
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'appId', 'appId');
    }

    /**
     * 获取关联的SKU
     */
    public function skus()
    {
        return $this->belongsToMany(AppSku::class, 'app_sku_runtime', 'appRuntimeId', 'appSkuId');
    }

    /**
     * 获取运行时环境名称
     */
    public function getRuntimeNameAttr()
    {
        $runtimeNames = [
            0 => '安卓',
            1 => 'iOS',
            3 => 'Web'
        ];

        return $runtimeNames[$this->runtimeId] ?? '未知环境';
    }
}
