{extend name="common/base" /}

{block name="title"}{$app.title} - 应用详情 - 应用市场管理系统{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow p-4 md:p-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <h2 class="text-xl font-semibold text-gray-800">应用详情</h2>
                <a href="{__MPRE__}{:url('app/edit', ['appId' => $app.appId])}" class="w-full sm:w-auto px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-center">
                    <i class="iconfont icon-edit"></i> 编辑应用
                </a>
            </div>

            <!-- 应用基本信息卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-3"></div>
                <div class="p-4 md:p-6">
                    <div class="flex flex-col sm:flex-row items-start">
                        <div class="flex items-center justify-center w-full sm:w-auto mb-4 sm:mb-0">
                            <img src="//i.cdn.yimenapp.com/{$app.logo}" alt="{$app.title}" class="w-20 h-20 sm:w-24 sm:h-24 rounded-lg shadow-sm sm:mr-6 object-cover">
                        </div>
                        <div class="flex-1 w-full">
                            <div class="flex flex-col sm:flex-row justify-between items-start gap-3 sm:gap-0">
                                <div>
                                    <h3 class="text-xl font-bold text-gray-800">{$app.title}</h3>
                                    <p class="text-sm text-gray-500 mt-1">AppCode: {$app.appCode}</p>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {$app.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                        <i class="iconfont {$app.isOnline ? 'icon-online' : 'icon-offline'} mr-1"></i>
                                        {$app.isOnline ? '已上线' : '未上线'}
                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {$app.isPrivate ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}">
                                        <i class="iconfont {$app.isPrivate ? 'icon-app' : 'icon-app'} mr-1"></i>
                                        {$app.isPrivate ? '私有应用' : '公有应用'}
                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                        {switch name="app.status"}
                                            {case value="0"}bg-gray-100 text-gray-800{/case}
                                            {case value="1"}bg-yellow-100 text-yellow-800{/case}
                                            {case value="2"}bg-green-100 text-green-800{/case}
                                            {default /}bg-gray-100 text-gray-800
                                        {/switch}">
                                        <i class="iconfont icon-detail mr-1"></i>
                                        {switch name="app.status"}
                                            {case value="0"}编辑中{/case}
                                            {case value="1"}审核中{/case}
                                            {case value="2"}审核通过{/case}
                                            {default /}未知状态
                                        {/switch}
                                    </span>
                                </div>
                            </div>
                            <p class="text-gray-600 mt-3 mb-4 break-words">{$app.description}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计信息卡片 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-6">
                <!-- 付费数统计 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-4 md:p-6">
                    <div class="flex items-center justify-between mb-3 md:mb-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-700">付费统计</h3>
                        <span class="inline-flex items-center justify-center p-2 bg-indigo-100 text-indigo-600 rounded-full">
                            <i class="iconfont icon-package text-lg md:text-xl"></i>
                        </span>
                    </div>
                    <div class="flex items-baseline">
                        <span class="text-2xl md:text-3xl font-bold text-indigo-600">{$app.paidCount}</span>
                        <span class="ml-2 text-xs md:text-sm text-gray-500">总付费数</span>
                    </div>
                </div>

                <!-- 环境数量统计 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-4 md:p-6">
                    <div class="flex items-center justify-between mb-3 md:mb-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-700">已配置环境</h3>
                        <span class="inline-flex items-center justify-center p-2 bg-orange-100 text-orange-600 rounded-full">
                            <i class="iconfont icon-runtime text-lg md:text-xl"></i>
                        </span>
                    </div>
                    <div class="flex items-baseline">
                        <span class="text-2xl md:text-3xl font-bold text-orange-600">{:count($runtimes)}</span>
                        <span class="ml-2 text-xs md:text-sm text-gray-500">个已配置环境</span>
                    </div>
                </div>

                <!-- 创建时间 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-4 md:p-6">
                    <div class="flex items-center justify-between mb-3 md:mb-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-700">创建时间</h3>
                        <span class="inline-flex items-center justify-center p-2 bg-green-100 text-green-600 rounded-full">
                            <i class="iconfont icon-detail text-lg md:text-xl"></i>
                        </span>
                    </div>
                    <div class="flex items-baseline">
                        <span class="text-sm md:text-lg font-medium text-gray-800">{:date('Y-m-d H:i', strtotime($app.createTime))}</span>
                    </div>
                </div>

                <!-- 状态信息 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-4 md:p-6">
                    <div class="flex items-center justify-between mb-3 md:mb-4">
                        <h3 class="text-base md:text-lg font-semibold text-gray-700">状态信息</h3>
                        <span class="inline-flex items-center justify-center p-2 bg-purple-100 text-purple-600 rounded-full">
                            <i class="iconfont icon-filter text-lg md:text-xl"></i>
                        </span>
                    </div>
                    <div>
                        {if $app.statusMessage}
                        <p class="text-gray-600 text-sm md:text-base break-words">{$app.statusMessage}</p>
                        {else}
                        <p class="text-gray-500 text-sm md:text-base">暂无状态信息</p>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 运行时环境卡片 -->
            {if !empty($runtimes)}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-orange-500 to-amber-500 h-2"></div>
                <div class="p-4 md:p-5">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                        <div class="flex items-center">
                            <i class="iconfont icon-runtime text-orange-500 mr-2"></i>
                            <h3 class="text-lg font-semibold text-gray-700">运行时环境</h3>
                        </div>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            共 <span class="font-bold mx-1">{:count($runtimes)}</span> 个环境
                        </span>
                    </div>
                    <div class="space-y-3 md:space-y-4">
                        {volist name="runtimes" id="item"}
                        <div class="bg-gray-50 rounded-lg p-3 md:p-4 border border-gray-200 hover:shadow-sm transition-shadow duration-300">
                            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
                                <div class="flex items-center">
                                    <i class="iconfont {$runtimeIcons[$item['runtimeId']] ?? 'icon-runtime'} text-indigo-600 text-lg md:text-xl mr-2"></i>
                                    <span class="text-sm md:text-md font-medium text-gray-700">{$runtimeNames[$item['runtimeId']] ?? '未知环境'}</span>
                                </div>
                                <div class="flex flex-wrap items-center gap-2 w-full sm:w-auto justify-start sm:justify-end">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$item['isOnline'] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                        <i class="iconfont {$item['isOnline'] ? 'icon-online' : 'icon-offline'} mr-1"></i>
                                        {$item['isOnline'] ? '已上线' : '未上线'}
                                    </span>
                                    
                                    <a href="{__MPRE__}{:url('appRuntime/edit', ['appId' => $app['appId'], 'runtimeId' => $item['runtimeId']])}" class="text-indigo-600 hover:text-indigo-800 inline-flex items-center">
                                        <i class="iconfont icon-edit mr-1"></i> 编辑
                                    </a>
                                </div>
                            </div>
                        </div>
                        {/volist}
                    </div>
                </div>
            </div>
            {/if}

            <!-- 配置信息卡片 -->
            {if $app.configSchema || $app.extra}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                {if $app.configSchema}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2"></div>
                    <div class="p-4 md:p-5">
                        <div class="flex items-center mb-3">
                            <i class="iconfont icon-detail text-blue-500 mr-2"></i>
                            <h4 class="text-sm md:text-md font-semibold text-gray-700">应用级配置表单</h4>
                        </div>
                        <div class="relative">
                            <pre class="bg-gray-50 p-3 md:p-4 rounded-md text-xs md:text-sm text-gray-700 overflow-auto max-h-60 md:max-h-80 border border-gray-200">{:json_encode($app.configSchema, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)}</pre>
                            <div class="absolute top-2 right-2">
                                <button class="copy-btn bg-gray-100 hover:bg-gray-200 text-gray-600 p-1 rounded" data-content="configSchema" title="复制内容">
                                    <i class="iconfont icon-copy text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {/if}

                {if $app.extra}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gradient-to-r from-green-500 to-teal-500 h-2"></div>
                    <div class="p-4 md:p-5">
                        <div class="flex items-center mb-3">
                            <i class="iconfont icon-filter text-green-500 mr-2"></i>
                            <h4 class="text-sm md:text-md font-semibold text-gray-700">扩展配置</h4>
                        </div>
                        <div class="relative">
                            <pre class="bg-gray-50 p-3 md:p-4 rounded-md text-xs md:text-sm text-gray-700 overflow-auto max-h-60 md:max-h-80 border border-gray-200">{:json_encode($app.extra, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)}</pre>
                            <div class="absolute top-2 right-2">
                                <button class="copy-btn bg-gray-100 hover:bg-gray-200 text-gray-600 p-1 rounded" data-content="extra" title="复制内容">
                                    <i class="iconfont icon-copy text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {/if}
            </div>
            {/if}

            <!-- 复制功能脚本 -->
            <script>
            document.addEventListener('DOMContentLoaded', function() {
                const copyButtons = document.querySelectorAll('.copy-btn');
                copyButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const contentType = this.getAttribute('data-content');
                        let content = '';

                        if (contentType === 'configSchema') {
                            content = JSON.stringify({$app.configSchema|json_encode}, null, 2);
                        } else if (contentType === 'extra') {
                            content = JSON.stringify({$app.extra|json_encode}, null, 2);
                        }

                        if (content) {
                            navigator.clipboard.writeText(content).then(() => {
                                // 显示复制成功提示
                                const originalTitle = this.getAttribute('title');
                                this.setAttribute('title', '复制成功!');
                                this.classList.add('bg-green-100', 'text-green-600');

                                setTimeout(() => {
                                    this.setAttribute('title', originalTitle);
                                    this.classList.remove('bg-green-100', 'text-green-600');
                                }, 2000);
                            }).catch(err => {
                                console.error('复制失败:', err);
                            });
                        }
                    });
                });
            });
            </script>
        </div>
    </div>
</div>
{/block}
