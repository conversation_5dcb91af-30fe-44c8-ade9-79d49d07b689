<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use app\model\AppRuntime;
use think\exception\HttpException;

/**
 * AppRuntime检查中间件
 * 用于检查AppRuntime是否存在，并将AppRuntime信息注入到请求中
 */
class AppRuntimeCheck
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取appId和runtimeId参数
        $appId = $request->param('appId');
        $runtimeId = $request->param('runtimeId');
        
        if (!$appId || $runtimeId === null) {
            throw new HttpException(400, '缺少必要参数');
        }
        
        // 查询AppRuntime信息
        $appRuntime = AppRuntime::where('appId', $appId)
            ->where('runtimeId', $runtimeId)
            ->find();
        
        // 将AppRuntime信息注入到请求中，即使为null也注入
        $request->appRuntime = $appRuntime;
        
        return $next($request);
    }
}
