<?php

declare(strict_types=1);

namespace app\service;

use think\facade\Cache;
use think\facade\Session;

/**
 * Yimen相关的服务
 */
class YmService
{

    public static function getConfig()
    {
        $config = self::getConfigFromApi("config");
        if($config){
            return $config;
        }
        return [
            "name" => "应用市场",
            "runtime" => [
                [
                    "label" => "移动版",
                    "icon" => "icon-mobile1",
                    "runtime" => [
                        [
                            "id" => 0,
                            "label" => "Android",
                            "icon" => "icon-android",
                            "desc" => "安卓 Android 移动版",
                            "pluginKey" => "plugins_adr"
                        ],
                        [
                            "id" => 1,
                            "label" => "iOS",
                            "icon" => "icon-apple",
                            "desc" => "苹果 iOS 移动版",
                            "pluginKey" => "plugins_ios"
                        ],
                        [
                            "id" => 9,
                            "label" => "Web",
                            "icon" => "icon-web",
                            "desc" => "移动浏览器版",
                            "pluginKey" => ""
                        ]
                    ]
                ],
                [
                    "label" => "电脑版",
                    "icon" => "icon-pc",
                    "runtime" => [
                        [
                            "id" => 10,
                            "label" => "Windows",
                            "icon" => "icon-windows",
                            "desc" => "微软 Windows 电脑版",
                            "pluginKey" => "plugins_win2"
                        ],
                        [
                            "id" => 11,
                            "label" => "macOS",
                            "icon" => "icon-macos",
                            "desc" => "苹果 macOS 电脑版",
                            "pluginKey" => "plugins_mac2"
                        ],
                        [
                            "id" => 12,
                            "label" => "Linux",
                            "icon" => "icon-linux",
                            "desc" => "Linux 电脑版",
                            "pluginKey" => "plugins_lnx2"
                        ],
                        [
                            "id" => 19,
                            "label" => "Web",
                            "icon" => "icon-web",
                            "desc" => "电脑浏览器版",
                            "pluginKey" => ""
                        ]
                    ]
                ],
                [
                    "label" => "小程序",
                    "icon" => "icon-xcx2",
                    "runtime" => [
                        [
                            "id" => 20,
                            "label" => "微信",
                            "icon" => "icon-xcx",
                            "desc" => "微信小程序",
                            "pluginKey" => ""
                        ],
                        [
                            "id" => 22,
                            "label" => "抖音/头条",
                            "icon" => "icon-douyin",
                            "desc" => "抖音/头条小程序",
                            "pluginKey" => ""
                        ],
                        [
                            "id" => 26,
                            "label" => "快手",
                            "icon" => "icon-kwai",
                            "desc" => "快手小程序",
                            "pluginKey" => ""
                        ],
                        [
                            "id" => 25,
                            "label" => "百度",
                            "icon" => "icon-baidu",
                            "desc" => "百度小程序",
                            "pluginKey" => ""
                        ],
                        [
                            "id" => 21,
                            "label" => "支付宝",
                            "icon" => "icon-zhifubao1",
                            "desc" => "支付宝小程序",
                            "pluginKey" => ""
                        ],
                        [
                            "id" => 23,
                            "label" => "QQ",
                            "icon" => "icon-qq",
                            "desc" => "QQ 小程序",
                            "pluginKey" => ""
                        ]
                    ]
                ]
            ],
            "icon" => "//at.alicdn.com/t/c/font_249925_pkmvbtu4pt.css",
            "cdn" => "//i.cdn.yimenapp.com"
        ];
    }


    public static function getPlguns()
    {
        $config = self::getConfigFromApi("plugins");
        if($config){
            return $config;
        }
        return [
            "icon_css" => "//at.alicdn.com/t/c/font_249925_pkmvbtu4pt.css",
            "tags" => [
                [
                    [
                        "id" => 2002,
                        "name" => "腾讯"
                    ],
                    [
                        "id" => 2003,
                        "name" => "阿里"
                    ],
                    [
                        "id" => 2004,
                        "name" => "百度"
                    ],
                    [
                        "id" => 2028,
                        "name" => "网易"
                    ],
                    [
                        "id" => 2027,
                        "name" => "苹果"
                    ],
                    [
                        "id" => 2022,
                        "name" => "个推"
                    ],
                    [
                        "id" => 2023,
                        "name" => "极光"
                    ],
                    [
                        "id" => 2021,
                        "name" => "友盟"
                    ],
                    [
                        "id" => 2020,
                        "name" => "头条"
                    ],
                    [
                        "id" => 2000,
                        "name" => "新浪"
                    ],
                    [
                        "id" => 2007,
                        "name" => "广告"
                    ],
                    [
                        "id" => 2026,
                        "name" => "其他厂商"
                    ]
                ],
                [
                    [
                        "id" => 2010,
                        "name" => "支付"
                    ],
                    [
                        "id" => 2011,
                        "name" => "登录"
                    ],
                    [
                        "id" => 2012,
                        "name" => "分享"
                    ],
                    [
                        "id" => 2018,
                        "name" => "存储"
                    ],
                    [
                        "id" => 2016,
                        "name" => "AI智能"
                    ],
                    [
                        "id" => 2005,
                        "name" => "消息推送"
                    ],
                    [
                        "id" => 2029,
                        "name" => "即时通讯"
                    ],
                    [
                        "id" => 2006,
                        "name" => "音视频"
                    ],
                    [
                        "id" => 2013,
                        "name" => "移动统计"
                    ],
                    [
                        "id" => 2015,
                        "name" => "代码注入"
                    ],
                    [
                        "id" => 2038,
                        "name" => "海外"
                    ]
                ],
                [
                    [
                        "id" => 2025,
                        "name" => "UI/界面"
                    ],
                    [
                        "id" => 2017,
                        "name" => "相机"
                    ],
                    [
                        "id" => 2024,
                        "name" => "麦克风"
                    ],
                    [
                        "id" => 2001,
                        "name" => "打印机"
                    ],
                    [
                        "id" => 2014,
                        "name" => "定位/地图"
                    ],
                    [
                        "id" => 2008,
                        "name" => "传感器"
                    ],
                    [
                        "id" => 2030,
                        "name" => "外设硬件"
                    ],
                    [
                        "id" => 2019,
                        "name" => "设备信息"
                    ],
                    [
                        "id" => 2039,
                        "name" => "其他"
                    ]
                ]
            ],
            "plugins" => [
                [
                    "id" => 0,
                    "name" => "正式版",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-author",
                    "description" => "免费试用7天，期间有“试用版”提示；此为必选功能，开通后请务必重新打包并安装新版。",
                    "adr_price" => 48,
                    "ios_price" => 128,
                    "win_price" => 98,
                    "mac_price" => 168,
                    "lnx_price" => 168
                ],
                [
                    "id" => 79,
                    "name" => "窗口样式",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-window",
                    "description" => "设置软件整体窗口的默认大小和缩放规则，支持全屏或满屏或自定义窗口比列大小，可禁止缩放。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 28,
                    "mac_price" => 28,
                    "lnx_price" => 28
                ],
                [
                    "id" => 70,
                    "name" => "标题栏",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-multi",
                    "description" => "设置应用多开程序以及退出状态，可多线程打开软件运行，可设置直接退出或推出后台运行。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 18,
                    "mac_price" => 18,
                    "lnx_price" => 18
                ],
                [
                    "id" => 71,
                    "name" => "启动欢迎屏",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-dengdai",
                    "description" => "软件启动时候电脑桌面出现一个启动欢迎屏窗口，自定义上传图片或图片链接，自定义窗口大小。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 18,
                    "mac_price" => 18,
                    "lnx_price" => 18
                ],
                [
                    "id" => 41,
                    "name" => "域名与版权",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-www1",
                    "description" => "一键使用CDN方式接入自有域名绑定到云商城，独立域名，可自定义底部版权信息，支持HTTPS。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 1,
                    "name" => "原生标题栏",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-biaoti",
                    "description" => "手机APP内顶部原生标题栏，可设置多项常规操作功能，比如分享、扫一扫、返回按钮等等。",
                    "adr_price" => 98,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 42,
                    "name" => "状态栏/标题栏",
                    "tag_ids" => [
                        2039,
                        2025
                    ],
                    "icon" => "icon-biaoti",
                    "description" => "手机APP内顶部标题栏，可自定义配色，可设置多项常规操作功能，比如扫一扫、侧滑边栏按钮。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 52,
                    "name" => "侧边栏",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-sidebar",
                    "description" => "APP内向右滑动页面拉出侧边栏，原生效果体验，自定义侧滑边栏导航，可视配置，实时生效。",
                    "adr_price" => 108,
                    "ios_price" => 138,
                    "win_price" => 98,
                    "mac_price" => 98,
                    "lnx_price" => 98
                ],
                [
                    "id" => 53,
                    "name" => "系统导航与后退",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-navbar",
                    "description" => "设置安卓5.0+版本系统虚拟导航及苹果X底部横线区域背景色和高度，苹果设备是否可滑屏后退。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 27,
                    "name" => "浮动功能面板",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-anniubutton4",
                    "description" => "APP全局显示一个浮动按钮，点击出现面板，可放置分享、扫一扫、前进、后退等功能图标方便操作。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 69,
                    "name" => "多窗口标签",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-tab",
                    "description" => "开启功能后会在标题栏下方 显示标签栏 用户可以打开多个标签页面同时浏览",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 18,
                    "mac_price" => 18,
                    "lnx_price" => 18
                ],
                [
                    "id" => 78,
                    "name" => "右键菜单",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-pop",
                    "description" => "软件内鼠标右键点击之后显示菜单，自定义面板，支持前进后退复制粘贴剪切全选等功能。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 15,
                    "name" => "加载进度动画",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-loading",
                    "description" => "页面加载动画，用于白屏过渡提升用户体验！",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 16,
                    "name" => "注入CSS样式表",
                    "tag_ids" => [
                        2015,
                        2025
                    ],
                    "icon" => "icon-CSS1",
                    "description" => "自定义CSS样式表以调整APP内界面UI，无需改动页面源代码，一键注入影响APP所有页面元素。",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 17,
                    "name" => "注入 JS 脚本",
                    "tag_ids" => [
                        2015,
                        2025
                    ],
                    "icon" => "icon-jszhuanhuan",
                    "description" => "注入自定义JS脚本用于增强页面功能以更好的适配App，无需修改服务器端页面源代码，在线配置。",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 28,
                    "name" => "URL规则",
                    "tag_ids" => [
                        2039,
                        2015
                    ],
                    "icon" => "icon-zhengzeshi",
                    "description" => "用正则表达式匹配 URL 链接来控制页面在本窗口打开、新窗口打开或系统浏览器中打开。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 3,
                    "name" => "下拉刷新",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-482gesturepulltorefresh",
                    "description" => "APP内下拉页面实现刷新功能，重新加载页面数据，可全局开启，也可指定页面开启。",
                    "adr_price" => 78,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 6,
                    "name" => "缓存管理",
                    "tag_ids" => [
                        2018
                    ],
                    "icon" => "icon-qingchuhuancun",
                    "description" => "APP内缓存管理功能，获取缓存大小，清理缓存，清除Cookie，可设置APP启动自动执行清理。",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 7,
                    "name" => "底部/顶部导航",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-1409gongjutiaokongjian",
                    "description" => "一键在线设置APP顶部导航栏或底部导航栏，二选一，按钮支持脚本，可自定义网址导航。",
                    "adr_price" => 68,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 29,
                    "name" => "底部工具栏",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-code",
                    "description" => "显示在APP窗口最底部的一栏快捷工具导航， 包括后退、前进、刷新、清理（缓存）、主页等按钮。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 14,
                    "name" => "长按",
                    "tag_ids" => [
                        2025,
                        2039
                    ],
                    "icon" => "icon-changan",
                    "description" => "APP内长按页面元素出现对应的功能面板，支持长按页面图片、文字、链接等实现快捷操控。",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 21,
                    "name" => "无网提示",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-nowifi",
                    "description" => "如果识别到APP无法获取网络数据或目标服务器返回错误代码，自动拦截出现原生无网提示页面。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 18,
                    "name" => "拍照/录制上传",
                    "tag_ids" => [
                        2017,
                        2018,
                        2024
                    ],
                    "icon" => "icon-paizhaoshangchuan",
                    "description" => "APP文件上传解决方案，设备相机拍照上传，录制视频、音频上传，选择设备本地文件上传。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 19,
                    "name" => "屏幕常亮",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-light",
                    "description" => "APP启动之后屏幕常亮，不受设备系统屏保限制，长期常亮状态，适合各种广告或展示类应用。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 26,
                    "name" => "多图浏览",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-pictures",
                    "description" => "APP内双击屏幕进入图片浏览模式，可快捷保存图片到相册，可一键分享图片到社交应用。",
                    "adr_price" => 108,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 25,
                    "name" => "返回与退出",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-exit",
                    "description" => "APP内返回和退出功能，控制系统返回和页面返回逻辑，控制APP结束进程退出或后台运行退出。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 31,
                    "name" => "设备适配",
                    "tag_ids" => [
                        2019,
                        2025
                    ],
                    "icon" => "icon-hengpingzhuanshuping",
                    "description" => "设置APP兼容手机或平板，适配横屏、竖屏或自适应，可开启全屏隐藏设备顶部状态栏。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 214,
                    "name" => "广播消息",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-apps2",
                    "description" => "应用内或应用间数据广播通信",
                    "adr_price" => 68,
                    "ios_price" => 68,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 54,
                    "name" => "APP截屏",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-jieping",
                    "description" => "设置APP内截屏功能效果，可截可视区域，也可以截整个页面，可自定义截屏是否同步分享图片。",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 196,
                    "name" => "壁纸/墙纸",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-bizhi",
                    "description" => "提供设备桌面及锁屏壁纸操作相关接口",
                    "adr_price" => 128,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 55,
                    "name" => "多屏异显",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-displays",
                    "description" => "适用于多屏安卓设备，有两块或多块屏幕的安卓设备，可分别控制每一块屏幕显示内容。",
                    "adr_price" => 98,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 13,
                    "name" => "浏览器UA",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-liulanqi",
                    "description" => "设置自定义浏览器UA，无需重新打包，在线配置",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 28,
                    "mac_price" => 28,
                    "lnx_price" => 28
                ],
                [
                    "id" => 22,
                    "name" => "通讯录",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-tongxunlu",
                    "description" => "APP获取通讯录权限解决方案，获取通讯录联系人姓名、电话、邮件等信息，需二次开发自行定义。",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 23,
                    "name" => "一键分享多张图",
                    "tag_ids" => [
                        2012
                    ],
                    "icon" => "icon-duotuwen",
                    "description" => "APP内一键分享最多9张图片到社交应用，支持微信朋友圈、QQ空间、微博、脸书等。",
                    "adr_price" => 898,
                    "ios_price" => 998,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 24,
                    "name" => "APP跳转",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-weibiaoti1",
                    "description" => "APP内点击链接或按钮启动其他第三方APP，可正则匹配设置允许跳转的APP URL Scheme。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 60,
                    "name" => "视频播放器",
                    "tag_ids" => [
                        2006
                    ],
                    "icon" => "icon-video",
                    "description" => "集成第三方阿里原生播放器，支持mp4、hls(m3u8)、flv点播、hls(m3u8)、rtmp、flv直播等。",
                    "adr_price" => 108,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 45,
                    "name" => "URI 启动",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-schema",
                    "description" => "获取APP的URL scheme，在html页面内唤起当前APP并打开对应的网页，可实现URL启动APP。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 94,
                    "name" => "开机自动启动",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-boot",
                    "description" => "安卓APP开机自动启动，Android 9 及以下版本设备开机之后APP紧跟系统自动启动。",
                    "adr_price" => 88,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 111,
                    "name" => "短信",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-wsmp-sendSMS",
                    "description" => "APP获取设备短信读取权限，需手动授权APP读取短信权限，可获取设备短信列表及短信内容。",
                    "adr_price" => 78,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 128,
                    "name" => "拨打电话",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-call",
                    "description" => "",
                    "adr_price" => 38,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 115,
                    "name" => "自定义渠道安装",
                    "tag_ids" => [
                        2013
                    ],
                    "icon" => " icon-tongji",
                    "description" => "高效APP推广统计技术，无须渠道分包、免填推广码，实现海量渠道APP安装业绩归属(CPS)统计。",
                    "adr_price" => 168,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 167,
                    "name" => "VPN",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-key",
                    "description" => "",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 170,
                    "name" => "悬浮窗口",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-floater",
                    "description" => "悬浮窗口 SYSTEM ALERT WINDOW",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 47,
                    "name" => "百度移动统计",
                    "tag_ids" => [
                        2013,
                        2004
                    ],
                    "icon" => "icon-tongji",
                    "description" => "集成第三方百度移动统计SDK，APP使用百度提供的移动统计分析工具，快捷查看应用核心数据。",
                    "adr_price" => 48,
                    "ios_price" => 58,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 48,
                    "name" => "百度地理定位",
                    "tag_ids" => [
                        2004,
                        2014
                    ],
                    "icon" => "icon-baiduloc",
                    "description" => "集成第三方百度地理定位SDK，百度地图提供原生定位服务，APP获取设备经纬度信息，需二次开发。",
                    "adr_price" => 48,
                    "ios_price" => 58,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 181,
                    "name" => "百度导航",
                    "tag_ids" => [
                        2014,
                        2004
                    ],
                    "icon" => "icon-baiduloc",
                    "description" => "支持驾车、骑行及步行多种导航方式",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 10,
                    "name" => "AI 文字识别",
                    "tag_ids" => [
                        2016,
                        2004
                    ],
                    "icon" => "icon-ai",
                    "description" => "集成第三方百度AI文字识别SDK，百度开放平台提供AI服务完成对文字的智能识别，需二次开发。",
                    "adr_price" => 108,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 57,
                    "name" => "AI 人脸识别",
                    "tag_ids" => [
                        2004,
                        2016
                    ],
                    "icon" => "icon-ai",
                    "description" => "集成第三方百度AI人脸识别SDK，百度开放平台提供AI服务完成对人脸的智能识别，需二次开发。",
                    "adr_price" => 108,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 33,
                    "name" => "微信分享",
                    "tag_ids" => [
                        2002,
                        2012
                    ],
                    "icon" => "icon-weixin",
                    "description" => "集成第三方腾讯微信分享SDK，APP唤起微信客户端分享页面到好友或朋友圈，可自定义分享内容。",
                    "adr_price" => 98,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 11,
                    "name" => "微信登录",
                    "tag_ids" => [
                        2002,
                        2011
                    ],
                    "icon" => "icon-weixin",
                    "description" => "集成第三方腾讯微信登录SDK，APP唤起微信客户端授权用户信息实现快捷登录，需二次开发。",
                    "adr_price" => 68,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 38,
                    "name" => "微信支付",
                    "tag_ids" => [
                        2002,
                        2010
                    ],
                    "icon" => "icon-weizhifu",
                    "description" => "集成第三方腾讯微信支付SDK，腾讯旗下移动支付解决方案，实现APP唤起微信完成便捷支付。",
                    "adr_price" => 288,
                    "ios_price" => 288,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 72,
                    "name" => "微信一次性消息",
                    "tag_ids" => [
                        2002
                    ],
                    "icon" => "icon-dingyue",
                    "description" => "接入微信一次性消息SDK，APP唤起微信客户端获得用户授权，用户即可收到微信下发消息通知。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 73,
                    "name" => "拉起微信小程序",
                    "tag_ids" => [
                        2002
                    ],
                    "icon" => "icon-xcx",
                    "description" => "集成微信拉起小程序接口，APP可直接唤起微信并打开指定的小程序，建议开放平台申请的移动应用为上架状态。",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 118,
                    "name" => "微信内拉起APP",
                    "tag_ids" => [
                        2002
                    ],
                    "icon" => "icon-weibiaoti1",
                    "description" => "微信开放标签 wx-open-launch-app，在微信内访问网页可拉起并传递必要参数到 APP。",
                    "adr_price" => 38,
                    "ios_price" => 38,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 133,
                    "name" => "拉起微信客服",
                    "tag_ids" => [
                        2002
                    ],
                    "icon" => "icon-kefu",
                    "description" => "执行JS唤起企业微信客服，APP内拉起微信客服，APP在线客服咨询解决方案。",
                    "adr_price" => 38,
                    "ios_price" => 38,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 20,
                    "name" => "X5内核",
                    "tag_ids" => [
                        2006,
                        2002
                    ],
                    "icon" => "icon-5",
                    "description" => "集成第三方腾讯X5内核，整合腾讯底层浏览技术和腾讯平台资源及能力，可大幅改善应用体验。",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 184,
                    "name" => "企业微信",
                    "tag_ids" => [
                        2002
                    ],
                    "icon" => "icon-wwx",
                    "description" => "腾讯企业微信，提供授权登录、分享等接口。",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 34,
                    "name" => "QQ分享",
                    "tag_ids" => [
                        2002,
                        2012
                    ],
                    "icon" => "icon-qq",
                    "description" => "集成腾讯QQ分享SDK，APP唤起QQ客户端分享页面到好友或QQ空间，接口需到QQ互联申请。",
                    "adr_price" => 98,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 43,
                    "name" => "微信 (分享/登录/支付)",
                    "tag_ids" => [
                        2002,
                        2010,
                        2011,
                        2012
                    ],
                    "icon" => "icon-weixin",
                    "description" => "集成第三方腾讯微信分享、微信登录、微信支付，接口需到微信开放平台独立申请，配好即用。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 12,
                    "name" => "QQ登录",
                    "tag_ids" => [
                        2002,
                        2011
                    ],
                    "icon" => "icon-qq",
                    "description" => "集成第三方腾讯QQ登录SDK，APP唤起QQ客户端授权用户信息实现快捷登录，需二次开发。",
                    "adr_price" => 68,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 44,
                    "name" => "QQ (分享/登录)",
                    "tag_ids" => [
                        2002,
                        2012,
                        2011
                    ],
                    "icon" => "icon-qq",
                    "description" => "集成第三方腾讯QQ分享SDK、QQ登录SDK，接口需到QQ互联平台自行独立申请，配好即用。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 183,
                    "name" => "浏览文档",
                    "tag_ids" => [
                        2002
                    ],
                    "icon" => "icon-preview",
                    "description" => "腾讯云文档浏览服务",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 98,
                    "name" => "TRTC 实时音视频",
                    "tag_ids" => [
                        2006,
                        2002
                    ],
                    "icon" => "icon-im",
                    "description" => "集成第三方腾讯实时音视频（Tencent RTC）主打低延时互动直播和多人音视频两大解决方案。",
                    "adr_price" => 368,
                    "ios_price" => 368,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 174,
                    "name" => "IM 即时通信",
                    "tag_ids" => [
                        2002,
                        2006,
                        2029
                    ],
                    "icon" => "icon-im",
                    "description" => "腾讯云即时通信 IM",
                    "adr_price" => 398,
                    "ios_price" => 398,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 85,
                    "name" => "视立方 · 播放器",
                    "tag_ids" => [
                        2002,
                        2006
                    ],
                    "icon" => "icon-video",
                    "description" => "集成第三方腾讯超级播放器SDK，支持点播、直播、倍速、多码率、循环、横竖屏，需二次开发。",
                    "adr_price" => 128,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 151,
                    "name" => "腾讯地理定位",
                    "tag_ids" => [
                        2002,
                        2014
                    ],
                    "icon" => "icon-qqloc",
                    "description" => "提供优秀的位置解决方案，已为社交、出行、游戏、商业、O2O、物流等领域提供专业的定位服务。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 120,
                    "name" => "广点通数据上报",
                    "tag_ids" => [
                        2002,
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "腾讯广告（广点通）行为数据接入，主要用于在广点通投放广告效果统计使用！",
                    "adr_price" => 78,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 126,
                    "name" => "广点通/优量汇",
                    "tag_ids" => [
                        2007,
                        2002
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "腾讯广点通/优量汇广告变现",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 46,
                    "name" => "支付宝支付",
                    "tag_ids" => [
                        2010,
                        2003
                    ],
                    "icon" => "icon-zhifubao",
                    "description" => "集成第三方支付宝支付SDK，阿里旗下移动支付解决方案，实现APP唤起支付宝完成便捷支付。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 37,
                    "name" => "支付宝支付",
                    "tag_ids" => [
                        2003,
                        2010
                    ],
                    "icon" => "icon-zhifubao",
                    "description" => "集成第三方支付宝支付SDK，阿里旗下移动支付解决方案，实现APP唤起支付宝完成便捷支付。",
                    "adr_price" => 288,
                    "ios_price" => 288,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 101,
                    "name" => "支付宝登录",
                    "tag_ids" => [
                        2003,
                        2011
                    ],
                    "icon" => "icon-alipay3",
                    "description" => "集成第三方支付宝登录SDK，APP唤起支付宝客户端授权用户信息实现快捷登录，需二次开发。",
                    "adr_price" => 98,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 134,
                    "name" => "本机号码认证",
                    "tag_ids" => [
                        2003,
                        2011
                    ],
                    "icon" => "icon-mobile-check",
                    "description" => "号码认证服务整合三大运营商特有的网关认证能力，应用于用户注册、登陆、安全校验等场景，实现无感知校验。",
                    "adr_price" => 98,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 164,
                    "name" => "金融级实人认证",
                    "tag_ids" => [
                        2003,
                        2011,
                        2016
                    ],
                    "icon" => "icon-fv",
                    "description" => "阿里云金融级实人认证是对用户身份信息真实性核验的服务，验证用户为真人且为本人。",
                    "adr_price" => 288,
                    "ios_price" => 288,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 40,
                    "name" => "阿里百川标准版",
                    "tag_ids" => [
                        2003
                    ],
                    "icon" => "icon-taobao",
                    "description" => "集成第三方阿里百川电商SDK，导购APP唤起淘宝或天猫客户端领券购买商品，淘客类应用必备。",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 50,
                    "name" => "新浪微博",
                    "tag_ids" => [
                        2000,
                        2011,
                        2012,
                        2003
                    ],
                    "icon" => "icon-weibo",
                    "description" => "集成第三方新浪微博SDK，APP唤起微博实现分享或授权用户信息实现快捷登录，需二次开发。",
                    "adr_price" => 118,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 61,
                    "name" => "友盟统计",
                    "tag_ids" => [
                        2013,
                        2021
                    ],
                    "icon" => "icon-tongji",
                    "description" => "集成第三方友盟移动统计SDK，APP使用友盟提供的移动统计分析工具，快捷查看应用核心数据。",
                    "adr_price" => 48,
                    "ios_price" => 58,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 130,
                    "name" => "高德定位",
                    "tag_ids" => [
                        2014,
                        2003
                    ],
                    "icon" => "icon-amap-loc",
                    "description" => "高德地图原生地理定位",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 131,
                    "name" => "高德导航",
                    "tag_ids" => [
                        2014,
                        2003
                    ],
                    "icon" => "icon-amap-navi",
                    "description" => "高德地图原生线路导航",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 132,
                    "name" => "高德猎鹰轨迹",
                    "tag_ids" => [
                        2003,
                        2014
                    ],
                    "icon" => "icon-track",
                    "description" => "猎鹰提供轨迹纠偏、里程计算、实时监控等丰富的接口功能和云端服务，可以让开发者基于猎鹰迅速构建一套完全属于自己的精准、高效的轨迹管理系统，应用于车队管理、人员管理等领域。",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 162,
                    "name" => "实人认证",
                    "tag_ids" => [
                        2003,
                        2016,
                        2011
                    ],
                    "icon" => "icon-fv",
                    "description" => "阿里云实人认证是对用户身份信息真实性核验的服务，验证用户为真人且为本人。",
                    "adr_price" => 248,
                    "ios_price" => 248,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 163,
                    "name" => "增强版实人认证",
                    "tag_ids" => [
                        2003,
                        2016,
                        2011
                    ],
                    "icon" => "icon-fv",
                    "description" => "阿里云增强版实人认证是对用户身份信息真实性核验的服务，验证用户为真人且为本人。",
                    "adr_price" => 268,
                    "ios_price" => 268,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 39,
                    "name" => "个推推送",
                    "tag_ids" => [
                        2022,
                        2005
                    ],
                    "icon" => "icon-tuisong",
                    "description" => "集成第三方个推推送SDK，个推提供消息通知解决方案，APP获得单推、组推、群推等系统通知能力。",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 49,
                    "name" => "个推厂商推送",
                    "tag_ids" => [
                        2022,
                        2005
                    ],
                    "icon" => "icon-tuisong",
                    "description" => "集成第三方个推厂商推送SDK，个推提供VIP级消息通知解决方案，支持离线推送，需向个推付费。",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 36,
                    "name" => "极光推送",
                    "tag_ids" => [
                        2005,
                        2023
                    ],
                    "icon" => "icon-tuisong",
                    "description" => "集成第三方极光推送SDK，轻松地通过极光发送各个移动平台的系统通知，接口需向极光申请。",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 175,
                    "name" => "环信IM即时通讯",
                    "tag_ids" => [
                        2006,
                        2029
                    ],
                    "icon" => "icon-im",
                    "description" => "环信全球消息云 - 互联网的消息基础设施",
                    "adr_price" => 268,
                    "ios_price" => 298,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 84,
                    "name" => "本地通知",
                    "tag_ids" => [
                        2039,
                        2025
                    ],
                    "icon" => "icon-alert",
                    "description" => "向APP发送延时的或及时的本地通知，系统级消息展示，可设置 Badge 数字角标，需二次开发。",
                    "adr_price" => 28,
                    "ios_price" => 28,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 9,
                    "name" => "H5地理定位",
                    "tag_ids" => [
                        2014
                    ],
                    "icon" => "icon-dingwei",
                    "description" => "APP直接兼容基于HTML5 标准地理位置定位，H5在浏览器里面可以定位则在APP内也可以定位。",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 74,
                    "name" => "OAID & IMEI 标识",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-oaid",
                    "description" => "OAID（Open Anonymous Identifier）是一种在Android设备上使用的匿名设备标识符，旨在替代传统的设备标识符如IMEI，以保护用户隐私。",
                    "adr_price" => 18,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 198,
                    "name" => "OAID 匿名设备标识",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-oaid",
                    "description" => "MSA 移动安全联盟",
                    "adr_price" => 68,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 200,
                    "name" => "IMEI",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-imei",
                    "description" => "设备 IMEI 标识符",
                    "adr_price" => 18,
                    "ios_price" => 28,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 5,
                    "name" => "指纹面容验证",
                    "tag_ids" => [
                        2008
                    ],
                    "icon" => "icon-fingerprint",
                    "description" => "Android 6.0+ 指纹验证、iOS 面容/指纹验证（FaceID/TouchID）用于设备本人操作安全验证使用。",
                    "adr_price" => 48,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 199,
                    "name" => "电池",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-battery",
                    "description" => "获取、监听电池电量及充放电状态。",
                    "adr_price" => 18,
                    "ios_price" => 28,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 201,
                    "name" => "模拟器检测",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-mobile-check",
                    "description" => "检测应用运行环境是否为模拟器",
                    "adr_price" => 28,
                    "ios_price" => 28,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 83,
                    "name" => "网络信息",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-network",
                    "description" => "APP获取设备当前IP地址信息、Wifi热点信息 、网络连接状态等信息，需二次开发。",
                    "adr_price" => 18,
                    "ios_price" => 28,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 106,
                    "name" => "NFC 近场通信",
                    "tag_ids" => [
                        2008
                    ],
                    "icon" => "icon-nfc",
                    "description" => "APP获得设备NFC功能控制权限，实现应用NFC近场通信，适用于有NFC需求行业，需二次开发。",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 82,
                    "name" => "蓝牙 BLE",
                    "tag_ids" => [
                        2019,
                        2008
                    ],
                    "icon" => "icon-ble",
                    "description" => "APP获得设备蓝牙权限，支持蓝牙4.0即BLE低功耗版， iOS6.0 、Android 4.3 以上，需二次开发。",
                    "adr_price" => 168,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 207,
                    "name" => "USB",
                    "tag_ids" => [
                        2008,
                        2030
                    ],
                    "icon" => "icon-usb",
                    "description" => "提供 USB Host 主机模式的设备操作接口",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 205,
                    "name" => "音频",
                    "tag_ids" => [
                        2008,
                        2030,
                        2019
                    ],
                    "icon" => "icon-bf",
                    "description" => "提供播放音量调节、静音开关等操作接口。",
                    "adr_price" => 68,
                    "ios_price" => 68,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 206,
                    "name" => "屏幕",
                    "tag_ids" => [
                        2030,
                        2008,
                        2019
                    ],
                    "icon" => "icon-mobile1",
                    "description" => "提供屏幕亮度调节、常亮开关等操作接口。",
                    "adr_price" => 68,
                    "ios_price" => 68,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 58,
                    "name" => "加速计",
                    "tag_ids" => [
                        2008
                    ],
                    "icon" => "icon-sensor",
                    "description" => "APP获得设备手机加速传感器控制权限，可获取加速计回传XYZ值，广泛用于摇一摇，计步使用。",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 59,
                    "name" => "陀螺仪",
                    "tag_ids" => [
                        2008
                    ],
                    "icon" => "icon-sensor",
                    "description" => "APP获得设备角速度传感器使用权限，获取陀螺仪回传XYZ数值，广泛用于游戏、VR、3D等场景。",
                    "adr_price" => 38,
                    "ios_price" => 48,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 138,
                    "name" => "串口通信",
                    "tag_ids" => [
                        2030,
                        2008
                    ],
                    "icon" => "icon-serialport",
                    "description" => "串口 (SerialPort、COM) 双向数据读写通信",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 159,
                    "name" => "托盘菜单",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-pop",
                    "description" => "设置桌面托盘菜单，包括右键和悬浮菜单！",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 88,
                    "mac_price" => 88,
                    "lnx_price" => 88
                ],
                [
                    "id" => 168,
                    "name" => "浮动按钮",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-floater",
                    "description" => "设置悬浮在页面上的按钮及其操作菜单",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 208,
                    "name" => "单机授权(一机一码)",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-license",
                    "description" => "单机授权",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 168,
                    "mac_price" => 168,
                    "lnx_price" => 168
                ],
                [
                    "id" => 202,
                    "name" => "单开/多开模式",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-schema",
                    "description" => "配置为单实例（单开）或者多实例（多开）运行模式",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 28,
                    "mac_price" => 28,
                    "lnx_price" => 28
                ],
                [
                    "id" => 211,
                    "name" => "下载管理",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-xiazai",
                    "description" => "支持自动捕获链接、自动识别文件名、批量下载、断点续传等特性。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 121,
                    "name" => "开机自动启动",
                    "tag_ids" => [
                        2039
                    ],
                    "icon" => "icon-boot",
                    "description" => "电脑开机时自动启动应用",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 76,
                    "name" => "用户协议与隐私",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-yhxy",
                    "description" => "在线设置用户协议与隐私，在首次打开 APP时自动弹出或调用，需用户点击同意后才可以继续操作。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 77,
                    "name" => "安装目录",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-forward-l-bi",
                    "description" => "可自定义软件默认安装目录，个性化的设置软件默认安装文件夹名，只支持windows版。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 171,
                    "name" => "视频启动屏",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-amin-splash",
                    "description" => "MP4 视频动画欢迎启动屏",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 32,
                    "name" => "广告启动屏",
                    "tag_ids" => [
                        2007,
                        2025
                    ],
                    "icon" => "icon-dengdai",
                    "description" => "在线设置广告启动屏，APP开机屏上半部分可动态实时更换，广泛用于各类广告或活动营销。",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 119,
                    "name" => "PDF 阅读器",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-pdf",
                    "description" => "",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 160,
                    "name" => "JS 弹窗",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-multi",
                    "description" => "",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 48,
                    "mac_price" => 48,
                    "lnx_price" => 48
                ],
                [
                    "id" => 169,
                    "name" => "下载管理器",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-downloader",
                    "description" => "",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 88,
                    "mac_price" => 88,
                    "lnx_price" => 88
                ],
                [
                    "id" => 4,
                    "name" => "引导页",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-jiaohuanniu",
                    "description" => "引导页一般用于APP首次打开展示，通常显示3至5张可以侧滑轮播图片，做操作引导或活动使用。",
                    "adr_price" => 78,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 66,
                    "name" => "人人商城",
                    "tag_ids" => [
                        2026
                    ],
                    "icon" => "icon-renren",
                    "description" => "针对微擎人人商城程序做了适配，可直接打包人人商城URL，已做好原生分享登录支付兼容。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 188,
                    "name" => "截屏",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-jieping",
                    "description" => "支持设置快捷键或者调用 js 接口截取屏幕图片",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 68,
                    "mac_price" => 68,
                    "lnx_price" => 68
                ],
                [
                    "id" => 189,
                    "name" => "快捷键",
                    "tag_ids" => [
                        2025
                    ],
                    "icon" => "icon-kjj",
                    "description" => "设置快捷键所对应执行的操作",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 185,
                    "name" => "蓝牙 Bluetooth",
                    "tag_ids" => [
                        2030
                    ],
                    "icon" => "icon-ble",
                    "description" => "授权获取浏览内核的 navigator.bluetooth 对象",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 98,
                    "mac_price" => 98,
                    "lnx_price" => 98
                ],
                [
                    "id" => 186,
                    "name" => "串口 Serial",
                    "tag_ids" => [
                        2030
                    ],
                    "icon" => "icon-serialport",
                    "description" => "授权获取浏览内核的 navigator.serial 对象",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 98,
                    "mac_price" => 98,
                    "lnx_price" => 98
                ],
                [
                    "id" => 187,
                    "name" => "USB",
                    "tag_ids" => [
                        2030
                    ],
                    "icon" => "icon-usb",
                    "description" => "授权获取浏览内核的 navigator.usb 对象",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 98,
                    "mac_price" => 98,
                    "lnx_price" => 98
                ],
                [
                    "id" => 192,
                    "name" => "多屏异显",
                    "tag_ids" => [
                        2025,
                        2030
                    ],
                    "icon" => "icon-displays",
                    "description" => "多屏幕各自显示指定的内容",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 98,
                    "mac_price" => 98,
                    "lnx_price" => 98
                ],
                [
                    "id" => 86,
                    "name" => "Adobe Flash",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-flash",
                    "description" => "软件兼容Adobe Flash，支持Adobe Flash Player 插件，可直接打开Flash音视频动画小游戏等。",
                    "adr_price" => 0,
                    "ios_price" => 0,
                    "win_price" => 168,
                    "mac_price" => 168,
                    "lnx_price" => 168
                ],
                [
                    "id" => 8,
                    "name" => "扫一扫",
                    "tag_ids" => [
                        2017,
                        2025
                    ],
                    "icon" => "icon-saoyisao",
                    "description" => "APP唤起设备相机自动识别扫码，支持二维码、条形码，支持嵌入式连续扫码，扫码获得数据结果后可二开自行处理。",
                    "adr_price" => 128,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 80,
                    "name" => "文件系统",
                    "tag_ids" => [
                        2018
                    ],
                    "icon" => "icon-fs",
                    "description" => "FileSystem文件系统，App端本地文件管理，支持打开文件、目录、下载、解压、哈希、分享等动作。",
                    "adr_price" => 48,
                    "ios_price" => 58,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 81,
                    "name" => "SQLite 数据库",
                    "tag_ids" => [
                        2018
                    ],
                    "icon" => "icon-db",
                    "description" => "Android/iOS系统内置SQLite数据库，支持SQLite各种语法和命令，开发请自行参阅SQLite教程。",
                    "adr_price" => 48,
                    "ios_price" => 58,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 87,
                    "name" => "录音",
                    "tag_ids" => [
                        2006,
                        2024
                    ],
                    "icon" => "icon-luyin",
                    "description" => "APP获取设备录音权限，支持 HTML Form 表单，支持录制、回放、上传音频文件，可自定义UI。",
                    "adr_price" => 98,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 51,
                    "name" => "阅读文档",
                    "tag_ids" => [
                        2025,
                        2002
                    ],
                    "icon" => "icon-preview",
                    "description" => "APP内直接打开各种常用的文档，包括pdf、doc、xls、ppt、txt等格式，适合在线阅读类应用。",
                    "adr_price" => 68,
                    "ios_price" => 78,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 176,
                    "name" => "穿山甲GroMore",
                    "tag_ids" => [
                        2020,
                        2007
                    ],
                    "icon" => "icon-toutiao",
                    "description" => "穿山甲 GroMore - 广告聚合竞价，极致变现提收",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 90,
                    "name" => "穿山甲广告变现",
                    "tag_ids" => [
                        2007,
                        2020
                    ],
                    "icon" => "icon-toutiao",
                    "description" => "集成第三方穿山甲广告SDK，应用接入穿山甲平台获取各种移动广告资源，助力APP实现流量变现。",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 195,
                    "name" => "抖音",
                    "tag_ids" => [
                        2011,
                        2020,
                        2017,
                        2012
                    ],
                    "icon" => "icon-douyin",
                    "description" => "提供抖音授权登录、分享、发布、拍摄等多个操作接口。",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 177,
                    "name" => "穿山甲内容输出",
                    "tag_ids" => [
                        2020,
                        2007,
                        2025
                    ],
                    "icon" => "icon-toutiao",
                    "description" => "内容联盟 - 整合内容和广告，收益与留存双收",
                    "adr_price" => 168,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 89,
                    "name" => "移动数据上报",
                    "tag_ids" => [
                        2020,
                        2007
                    ],
                    "icon" => "icon-tongji",
                    "description" => "集成第三方巨量引擎移动数据上报SDK，将用户app内的关键行为事件回传，用于头条广告质量优化。",
                    "adr_price" => 78,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 209,
                    "name" => "TopOn",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "移动广告变现收益提升工具",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 204,
                    "name" => "灰鲸广告联盟",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "一站式流量变现平台，流量变现全过程透明化，有效保证媒体的利益。",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 203,
                    "name" => "ToBid",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "精耕流量价值的聚合广告工具",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 190,
                    "name" => "richmob聚合广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "专为流量主打造的流量变现平台",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 180,
                    "name" => "趣游",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "趣游手游盒子",
                    "adr_price" => 88,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 161,
                    "name" => "麦智杰广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "专为流量主打造的流量变现平台",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 165,
                    "name" => "神蓍广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "自主流量变现平台",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 173,
                    "name" => "星纪聚合广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "专为流量主打造的流量变现平台",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 136,
                    "name" => "ZJ 广告",
                    "tag_ids" => [
                        2007,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "实现流量化最大价值，专业的流量变现系统让应用变现更简单高效 专注应用可持续增长。",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 178,
                    "name" => "赋能广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "专为流量主打造的流量变现平台",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 112,
                    "name" => "Bloom 广告(AD)",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方 Bloom 广告(AD) SDK，APP接入Bloom AD平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 139,
                    "name" => "Bloom 资讯(News)",
                    "tag_ids" => [
                        2026,
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方 Bloom 资讯(News) SDK，将广告嵌入图文资讯、短视频等媒体内容，流量变现！",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 143,
                    "name" => "Bloom 短视频",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "BloomAd 短视频",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 144,
                    "name" => "Bloom 游戏",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "BloomAd 游戏",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 91,
                    "name" => "新量象鱼玩盒子",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方新量象鱼玩盒子SDK，APP接入第三方广告联盟实现流量变现，需开通IMEI/OAID。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 93,
                    "name" => "飞马移动广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方飞马移动广告SDK，APP接入飞马广告联盟激励视频实现流量变现，需二次开发。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 92,
                    "name" => "HyAdXOpenSdk",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成三方HyAdXOpenSdk，APP接入激励视频广告实现流量变现，需二次开发。",
                    "adr_price" => 78,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 95,
                    "name" => "海星广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方海星广告Sdk，APP接入海星广告平台实现流量变现，需二次开发自定义广告位。",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 97,
                    "name" => "聚力阅盟小说",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方聚力阅盟小说Sdk，APP接入聚力阅盟平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 99,
                    "name" => "梦工厂小游戏",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方梦工场小游戏Sdk，APP接入梦工场小游戏平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 102,
                    "name" => "多游游戏广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方多游游戏广告Sdk，APP接入多游游戏平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 103,
                    "name" => "幂动广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方幂动广告Sdk，APP接入幂动广告平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 98,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 104,
                    "name" => "闲玩广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方闲玩广告Sdk，APP接入闲玩广告平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 105,
                    "name" => "一览好兔视频",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方一览好兔视频广告Sdk，APP接入一览好兔视频实现流量变现，需二次开发自定义。",
                    "adr_price" => 98,
                    "ios_price" => 128,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 107,
                    "name" => "享玩积分",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方享玩积分Sdk，APP接入享玩积分变现平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 78,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 108,
                    "name" => "百汇广告",
                    "tag_ids" => [
                        0
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "集成第三方百汇广告Sdk，APP接入百汇广告变现平台实现流量变现，需二次开发自定义。",
                    "adr_price" => 98,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 116,
                    "name" => "聚享玩",
                    "tag_ids" => [
                        2007,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "聚享玩 H5 方式接入 一键使用H5方式实现聚享玩平台接入 实现流量变现",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 124,
                    "name" => "Fusion广告",
                    "tag_ids" => [
                        2007,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "Fusion 融合广告 - 广告聚合平台",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 125,
                    "name" => "南枫小游戏",
                    "tag_ids" => [
                        2007,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "南枫小游戏 - 休闲游戏/广告聚合平台",
                    "adr_price" => 168,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 127,
                    "name" => "飞鸟广告",
                    "tag_ids" => [
                        2007,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "飞鸟广告引擎",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 146,
                    "name" => "西域 - 广告",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "西域传媒广告",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 147,
                    "name" => "西域 - 资讯",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "西域传媒 - 资讯",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 148,
                    "name" => "西域 - 短视频",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "西域传媒 - 短视频",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 149,
                    "name" => "西域 - 游戏",
                    "tag_ids" => [
                        2007
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "西域传媒 - 游戏",
                    "adr_price" => 88,
                    "ios_price" => 98,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 62,
                    "name" => "银联云闪付",
                    "tag_ids" => [
                        2010,
                        2026
                    ],
                    "icon" => "icon-yinlian",
                    "description" => "集成第三方银联云闪付SDK，云闪付是银联旗下移动支付解决方案，实现APP唤起云闪付便捷支付。",
                    "adr_price" => 288,
                    "ios_price" => 288,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 114,
                    "name" => "银联全民付",
                    "tag_ids" => [
                        2010,
                        2026
                    ],
                    "icon" => "icon-yinlian",
                    "description" => "集成第三方银联全民付SDK，银联商务旗下移动支付解决方案，支持微信支付、支付宝支付、云闪付。",
                    "adr_price" => 288,
                    "ios_price" => 288,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 63,
                    "name" => "农行掌银支付",
                    "tag_ids" => [
                        2010,
                        2026
                    ],
                    "icon" => "icon-nonghang",
                    "description" => "集成第三方农行掌银SDK，农业银行官方移动支付解决方案，实现APP唤起农行掌银完成支付。",
                    "adr_price" => 68,
                    "ios_price" => 78,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 68,
                    "name" => "工行 APP 支付",
                    "tag_ids" => [
                        2026,
                        2010
                    ],
                    "icon" => "icon-icbc",
                    "description" => "集成第三方工行支付SDK，工商银行官方移动支付解决方案，实现APP唤起工行APP完成支付。",
                    "adr_price" => 68,
                    "ios_price" => 78,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 117,
                    "name" => "中信银行支付",
                    "tag_ids" => [
                        2010,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "中信银行支付旗下移动支付解决方案，实现APP一键唤起中信银行客户端完成在线移动支付。",
                    "adr_price" => 168,
                    "ios_price" => 198,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 155,
                    "name" => "天融信 TopVPN",
                    "tag_ids" => [
                        2019
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "中国领先的网络安全服务商",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 30,
                    "name" => "iOS内购",
                    "tag_ids" => [
                        2010,
                        2027
                    ],
                    "icon" => "icon-iap",
                    "description" => "集成苹果IOS内购SDK，APP有涉及虚拟购物需接入IOS内购使用苹果支付并给apple公司分成30%。",
                    "adr_price" => 0,
                    "ios_price" => 688,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 135,
                    "name" => "AppleID 授权登录",
                    "tag_ids" => [
                        2011,
                        2027
                    ],
                    "icon" => "icon-apple",
                    "description" => "iOS Sign In with Apple，Apple ID 一键授权登录。",
                    "adr_price" => 0,
                    "ios_price" => 368,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 75,
                    "name" => "IDFA 广告标识符",
                    "tag_ids" => [
                        2019,
                        2027
                    ],
                    "icon" => "icon-idfa",
                    "description" => "获取苹果移动设备广告标识符 IDFA，主要用于苹果版APP的广告跟踪，数据统计等用途。",
                    "adr_price" => 0,
                    "ios_price" => 18,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 216,
                    "name" => "GTM 跟踪代码管理器",
                    "tag_ids" => [
                        2038
                    ],
                    "icon" => "icon-google",
                    "description" => "Google Tag Manager 跟踪代码管理器",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 193,
                    "name" => "Google 一键登录",
                    "tag_ids" => [
                        2011,
                        2038
                    ],
                    "icon" => "icon-google",
                    "description" => "使用用户的 Google 帐号登录您的应用",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 152,
                    "name" => "AppsFlyer 统计",
                    "tag_ids" => [
                        2013,
                        2038
                    ],
                    "icon" => "icon-tongji",
                    "description" => "全球领先的归因数据统计平台",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 166,
                    "name" => "Adjust 归因统计",
                    "tag_ids" => [
                        2038,
                        2013
                    ],
                    "icon" => "icon-tongji",
                    "description" => "针对应用营销旅程每一阶段的端到端解决方案",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 156,
                    "name" => "Facebook 应用事件",
                    "tag_ids" => [
                        2038,
                        2013
                    ],
                    "icon" => "icon-fb",
                    "description" => "Facebook App Events 应用事件",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 157,
                    "name" => "Facebook 登录",
                    "tag_ids" => [
                        2038,
                        2011
                    ],
                    "icon" => "icon-fb",
                    "description" => "用户在你的应用内拉起 Facebook 客户端发起授权登录",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 158,
                    "name" => "Facebook 分享",
                    "tag_ids" => [
                        2038,
                        2012
                    ],
                    "icon" => "icon-fb",
                    "description" => "用户将内容从您的应用分享到 Facebook",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 215,
                    "name" => "乐福衡器",
                    "tag_ids" => [
                        2030
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "深圳市乐福衡器有限公司",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 197,
                    "name" => "恩普特 EWG01P",
                    "tag_ids" => [
                        2008,
                        2030
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "恩普特 EWG01P 无线测速、测振、测温传感器。",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 213,
                    "name" => "TSC 打印机",
                    "tag_ids" => [
                        2001,
                        2030
                    ],
                    "icon" => "icon-printer",
                    "description" => "TSC 打印机",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 182,
                    "name" => "兄弟标签打印机",
                    "tag_ids" => [
                        2001
                    ],
                    "icon" => "icon-printer",
                    "description" => "兄弟标签打印机",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 109,
                    "name" => "芯烨标签打印机",
                    "tag_ids" => [
                        2001,
                        2026,
                        2030
                    ],
                    "icon" => "icon-printer",
                    "description" => "集成第三方芯烨打印机SDK，APP可通信芯烨打印机，实现APP内在线打印票据面单等功能。",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 179,
                    "name" => "芯烨智能打印机",
                    "tag_ids" => [
                        2001,
                        2030
                    ],
                    "icon" => "icon-printer",
                    "description" => "珠海芯烨智能打印机",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 137,
                    "name" => "X 打印机",
                    "tag_ids" => [
                        2001
                    ],
                    "icon" => "icon-printer",
                    "description" => "X 定制打印机",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 56,
                    "name" => "中崎打印机",
                    "tag_ids" => [
                        2001,
                        2026,
                        2030
                    ],
                    "icon" => "icon-printer",
                    "description" => "集成第三方中崎打印机SDK，APP可通信中崎小票打印机，实现APP内在线打印小票功能。",
                    "adr_price" => 58,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 123,
                    "name" => "NETUM 扫描枪",
                    "tag_ids" => [
                        2030,
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "迅镭(逊镭)NETUM条码扫描枪（已测试设备：C750便携式条码扫描枪）",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 140,
                    "name" => "东集 UHF RFID",
                    "tag_ids" => [
                        2030,
                        2008
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "东集 UHF RFID 手持设备 Android 版 Java SDK 的 js API 映射",
                    "adr_price" => 128,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 141,
                    "name" => "海康威视摄像机",
                    "tag_ids" => [
                        2030,
                        2017
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "海康威视网络摄像机，全天候适应，全场景感知，全要素提取，全数据关联！",
                    "adr_price" => 168,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 142,
                    "name" => "奥比3D传感相机",
                    "tag_ids" => [
                        2030,
                        2017
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "奥比中光3D传感摄像头能让硬件设备拥有一双感知环境的“智慧之眼”",
                    "adr_price" => 168,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 172,
                    "name" => "耀华称重",
                    "tag_ids" => [
                        2030
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "上海耀华称重系统有限公司",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 113,
                    "name" => "DLNA 投屏",
                    "tag_ids" => [
                        2006,
                        2030
                    ],
                    "icon" => "icon-dlna",
                    "description" => "集成DLNA 投屏功能，应用通过DLNA协议与大屏设备如电视，智慧屏等实现屏幕投射能力。",
                    "adr_price" => 288,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 129,
                    "name" => "QS 扫描",
                    "tag_ids" => [
                        2026,
                        2039,
                        2030
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "QS扫描",
                    "adr_price" => 68,
                    "ios_price" => 0,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 210,
                    "name" => "Freight 货运",
                    "tag_ids" => [
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "Freight 货运",
                    "adr_price" => 88,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 212,
                    "name" => "网络货运信息监测",
                    "tag_ids" => [
                        2026
                    ],
                    "icon" => "icon-sdk1",
                    "description" => "网络货运信息监测系统 wlhy.mot.gov.cn",
                    "adr_price" => 88,
                    "ios_price" => 88,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 191,
                    "name" => "网易云信 IM 即时通讯",
                    "tag_ids" => [
                        2006,
                        2028,
                        2029
                    ],
                    "icon" => "icon-im",
                    "description" => "提供私聊、群聊、聊天室、圈组等通讯能力。",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ],
                [
                    "id" => 194,
                    "name" => "网易云信 RTC 实时音视频",
                    "tag_ids" => [
                        2006,
                        2028,
                        2029
                    ],
                    "icon" => "icon-rtc",
                    "description" => "新一代音视频通话支持双声道 128Kbps 码率立体声高清音质，支持1080P高清画质。",
                    "adr_price" => 128,
                    "ios_price" => 168,
                    "win_price" => 0,
                    "mac_price" => 0,
                    "lnx_price" => 0
                ]
            ],
            "plugins_adr" => [
                0,
                1,
                52,
                53,
                27,
                15,
                16,
                17,
                28,
                3,
                6,
                7,
                29,
                14,
                21,
                18,
                19,
                26,
                25,
                31,
                214,
                54,
                196,
                55,
                13,
                22,
                23,
                24,
                60,
                45,
                94,
                111,
                128,
                115,
                167,
                170,
                47,
                48,
                181,
                10,
                57,
                33,
                11,
                38,
                72,
                73,
                118,
                133,
                20,
                184,
                34,
                12,
                183,
                98,
                174,
                85,
                151,
                120,
                126,
                37,
                101,
                134,
                164,
                40,
                50,
                61,
                130,
                131,
                132,
                162,
                163,
                39,
                49,
                36,
                175,
                84,
                9,
                74,
                198,
                200,
                5,
                199,
                201,
                83,
                106,
                82,
                207,
                205,
                206,
                58,
                59,
                138,
                76,
                171,
                32,
                4,
                66,
                8,
                80,
                81,
                87,
                51,
                176,
                90,
                195,
                177,
                89,
                204,
                203,
                190,
                180,
                161,
                165,
                173,
                136,
                178,
                112,
                139,
                143,
                144,
                91,
                93,
                92,
                95,
                97,
                99,
                102,
                103,
                104,
                105,
                107,
                108,
                116,
                124,
                125,
                127,
                146,
                147,
                148,
                149,
                62,
                114,
                63,
                68,
                117,
                155,
                216,
                193,
                152,
                166,
                156,
                157,
                158,
                215,
                197,
                213,
                182,
                109,
                179,
                137,
                56,
                123,
                140,
                141,
                142,
                172,
                113,
                129,
                210,
                212,
                191,
                194
            ],
            "plugins_ios" => [
                0,
                1,
                52,
                53,
                27,
                15,
                16,
                17,
                28,
                3,
                6,
                7,
                29,
                14,
                21,
                18,
                19,
                26,
                31,
                54,
                13,
                22,
                23,
                24,
                45,
                48,
                181,
                10,
                57,
                33,
                11,
                38,
                72,
                73,
                118,
                133,
                184,
                34,
                12,
                98,
                174,
                85,
                151,
                126,
                37,
                101,
                134,
                164,
                40,
                50,
                61,
                130,
                131,
                39,
                49,
                36,
                84,
                9,
                5,
                199,
                83,
                106,
                206,
                58,
                59,
                76,
                171,
                32,
                4,
                66,
                8,
                80,
                81,
                87,
                51,
                176,
                195,
                209,
                204,
                136,
                112,
                139,
                143,
                144,
                91,
                93,
                95,
                97,
                99,
                102,
                104,
                105,
                107,
                146,
                147,
                148,
                149,
                62,
                114,
                63,
                68,
                117,
                30,
                135,
                75,
                216,
                193,
                215,
                179,
                191,
                194
            ],
            "plugins_win1" => [
                0,
                79,
                70,
                71,
                69,
                78,
                16,
                17,
                121,
                77,
                119,
                86
            ],
            "plugins_mac1" => [
                0,
                79,
                70,
                71,
                69,
                78,
                16,
                17,
                121,
                77,
                119,
                86
            ],
            "plugins_lnx1" => [
                0,
                79,
                70,
                71,
                69,
                78,
                16,
                17,
                121,
                77,
                119,
                86
            ],
            "plugins_win2" => [
                0,
                79,
                70,
                71,
                52,
                69,
                78,
                159,
                168,
                208,
                15,
                16,
                17,
                28,
                202,
                211,
                121,
                77,
                119,
                160,
                169,
                13,
                21,
                45,
                188,
                189,
                185,
                186,
                187,
                192,
                86
            ],
            "plugins_mac2" => [
                0,
                79,
                70,
                71,
                52,
                69,
                78,
                159,
                168,
                208,
                15,
                16,
                17,
                28,
                202,
                211,
                121,
                77,
                119,
                160,
                169,
                13,
                21,
                45,
                188,
                189,
                185,
                186,
                187,
                192,
                86
            ],
            "plugins_lnx2" => [
                0,
                79,
                70,
                71,
                52,
                69,
                78,
                159,
                168,
                208,
                15,
                16,
                17,
                28,
                202,
                211,
                121,
                77,
                119,
                160,
                169,
                13,
                21,
                45,
                188,
                189,
                185,
                186,
                187,
                192,
                86
            ]
        ];
    }

    private static function getConfigFromApi($key) {
        
        $userId = request()->userId;
        $content = Session::get('ym_config_'.$userId);
        if(!$content){
            $content = file_get_contents("http://gate.open.yimenyun.com/xapi-market/?want=config&userId=".$userId);
            if($content){
                $content = json_decode($content,true);
                Session::set('ym_config_'.$userId,$content);
            }
        }
        if(!$content || !isset($content['d']) ||    !isset($content['d'][$key])){
            return false;
        }
        return  $content['d'][$key];
    }
}
