<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use app\model\App;
use think\exception\HttpException;

/**
 * App检查中间件
 * 用于检查App是否存在，并将App信息注入到请求中
 */
class AppCheck
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取appId参数
        $appId = $request->param('appId');
        
        if (!$appId) {
            throw new HttpException(400, '缺少appId参数');
        }
        
        // 查询App信息
        $app = App::find($appId);
        
        if (!$app) {
            throw new HttpException(404, '应用不存在');
        }
        
        // 将App信息注入到请求中
        $request->app = $app;
        
        return $next($request);
    }
}
