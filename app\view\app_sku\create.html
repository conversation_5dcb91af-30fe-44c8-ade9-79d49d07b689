{extend name="common/base" /}

{block name="title"}{$app.title} - 创建SKU - 应用市场管理系统{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="iconfont icon-tag text-indigo-600 mr-2"></i>创建SKU
                </h2>
                <a href="{__MPRE__}{:url('appSku/index', ['appId' => $app.appId])}"
                    class="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs flex items-center">
                    <i class="iconfont icon-back mr-1"></i> 返回列表
                </a>
            </div>

            <form id="skuForm" data-validate>
                <!-- 基本信息 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-info-circle text-indigo-500 mr-2"></i>基本信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="name" class="block text-xs font-medium text-gray-700 mb-1">SKU名称 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-tag text-xs"></i>
                                </span>
                                <input type="text" id="name" name="name" required maxlength="64" placeholder="输入SKU名称"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">如：Android, Android + iOS</p>
                        </div>

                        <div class="form-group">
                            <label for="description" class="block text-xs font-medium text-gray-700 mb-1">SKU说明</label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-description text-xs"></i>
                                </span>
                                <input type="text" id="description" name="description" maxlength="256"
                                    placeholder="输入SKU说明" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格信息 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-price text-indigo-500 mr-2"></i>价格信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label for="salePrice" class="block text-xs font-medium text-gray-700 mb-1">售价 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-price text-xs"></i>
                                </span>
                                <input type="number" id="salePrice" name="salePrice" required min="0" step="0.01"
                                    placeholder="0.00" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="fullPrice" class="block text-xs font-medium text-gray-700 mb-1">原价 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-price text-xs"></i>
                                </span>
                                <input type="number" id="fullPrice" name="fullPrice" required min="0" step="0.01"
                                    placeholder="0.00" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sort" class="block text-xs font-medium text-gray-700 mb-1">排序 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-sort text-xs"></i>
                                </span>
                                <input type="number" id="sort" name="sort" required min="0" value="0" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">数字越小排序越靠前</p>
                        </div>
                    </div>
                </div>
                <!-- 应用设置 -->
                <div class="mb-8">
                    <h2
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-setting text-indigo-500 mr-2"></i>状态设置
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">


                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-2">
                                上线状态 <span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-5 mt-1">
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="isOnline" value="0" checked class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span
                                        class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">未上线</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="isOnline" value="1" class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span
                                        class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">已上线</span>
                                </label>
                            </div>
                        </div>


                    </div>
                </div>
                <!-- 环境配置 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-environment text-indigo-500 mr-2"></i>环境配置
                    </h3>
                    <div class="form-group">
                        <label class="block text-xs font-medium text-gray-700 mb-2">选择运行时环境 <span
                                class="text-red-500">*</span></label>
                        <div class="p-4 border border-gray-200 rounded-md bg-gray-50">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                {volist name="runtimes" id="runtime"}
                                <label
                                    class="relative flex items-center p-2 rounded-md border border-gray-200 bg-white hover:border-indigo-300 transition-colors duration-200 cursor-pointer group">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <input type="checkbox" name="runtimeIds[]" value="{$runtime.runtimeId}"
                                                class="peer sr-only">
                                            <div class="w-4 h-4 border border-gray-300 rounded
                                                group-hover:border-indigo-400 peer-checked:bg-indigo-600
                                                peer-checked:border-indigo-600 transition-all duration-200"></div>
                                            <div
                                                class="absolute top-0 left-0 w-4 h-4 text-white flex items-center justify-center opacity-0 peer-checked:opacity-100">
                                                <i class="iconfont icon-check text-xs"></i>
                                            </div>
                                        </div>
                                        <div class="ml-2 text-sm">
                                            <div
                                                class="font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">
                                                {$runtime.runtimeName}</div>
                                        </div>
                                    </div>
                                </label>
                                {/volist}

                                {empty name="runtimes"}
                                <div
                                    class="md:col-span-3 p-4 bg-yellow-50 text-yellow-600 rounded-md flex items-center">
                                    <i class="iconfont icon-warning mr-2"></i> 请先创建运行时环境
                                </div>
                                {/empty}
                            </div>
                        </div>
                        <p class="mt-2 text-xs text-gray-500" id="selectedRuntimesCount">已选择 <span
                                class="font-medium text-indigo-600">0</span> 个环境</p>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-100">
                    <button type="button"
                        onclick="window.location.href='{__MPRE__}{:url(\'appSku/index\', [\'appId\' => $app.appId])}'"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-close mr-1"></i> 取消
                    </button>
                    <button type="button" id="submitButton" onclick="submitForm()" class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-save mr-1"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化表单元素
        initFormElements();

        // 初始化运行时环境选择
        initRuntimeSelection();
    });

    // 初始化表单元素
    function initFormElements() {
        // 添加输入框焦点效果
        const inputFields = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
        inputFields.forEach(field => {
            field.addEventListener('focus', function () {
                this.closest('.form-group')?.classList.add('is-focused');
            });

            field.addEventListener('blur', function () {
                this.closest('.form-group')?.classList.remove('is-focused');

                // 简单验证（如果是必填字段）
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('border-red-500');

                    // 添加错误提示
                    const fieldGroup = this.closest('.form-group');
                    if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                        const errorMsg = document.createElement('p');
                        errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                        errorMsg.textContent = '此字段不能为空';
                        fieldGroup.appendChild(errorMsg);
                    }
                } else {
                    this.classList.remove('border-red-500');

                    // 移除错误提示
                    const fieldGroup = this.closest('.form-group');
                    const errorMsg = fieldGroup?.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }
            });
        });

        // 价格输入框特殊处理
        const priceInputs = document.querySelectorAll('input[type="number"][step="0.01"]');
        priceInputs.forEach(input => {
            input.addEventListener('blur', function () {
                if (this.value) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            });
        });
    }

    // 初始化运行时环境选择
    function initRuntimeSelection() {
        const checkboxes = document.querySelectorAll('input[name="runtimeIds[]"]');
        const countElement = document.querySelector('#selectedRuntimesCount span');

        // 更新选中数量
        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('input[name="runtimeIds[]"]:checked').length;
            countElement.textContent = selectedCount;
        }

        // 添加事件监听器
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        // 初始更新
        updateSelectedCount();
    }

    // 验证表单
    function validateForm() {
        const form = document.getElementById('skuForm');
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        // 验证必填字段
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');

                // 添加抖动效果
                field.classList.add('animate-shake');
                setTimeout(() => {
                    field.classList.remove('animate-shake');
                }, 600);

                // 添加错误提示
                const fieldGroup = field.closest('.form-group');
                if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                    const errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                    errorMsg.textContent = '此字段不能为空';
                    fieldGroup.appendChild(errorMsg);
                }

                isValid = false;
            }
        });

        // 检查是否选择了运行时环境
        const runtimeIds = document.querySelectorAll('input[name="runtimeIds[]"]:checked');
        if (runtimeIds.length === 0) {
            const runtimesContainer = document.querySelector('.p-4.border.border-gray-200.rounded-md.bg-gray-50');
            runtimesContainer.classList.add('border-red-500', 'animate-shake');

            setTimeout(() => {
                runtimesContainer.classList.remove('animate-shake');
            }, 600);

            // 添加错误提示
            const fieldGroup = runtimesContainer.closest('.form-group');
            if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                const errorMsg = document.createElement('p');
                errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                errorMsg.textContent = '请至少选择一个运行时环境';
                fieldGroup.appendChild(errorMsg);
            }

            isValid = false;
        }

        // 如果有错误，滚动到第一个错误字段
        if (!isValid) {
            const firstErrorField = form.querySelector('.border-red-500');
            if (firstErrorField) {
                firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                if (firstErrorField.tagName !== 'DIV') {
                    firstErrorField.focus();
                }
            }
        }

        return isValid;
    }

    // 提交表单
    async function submitForm() {
        // 验证表单
        if (!validateForm()) {
            showNotification('表单验证失败，请检查输入', 'error');
            return;
        }

        // 显示加载状态
        const submitButton = document.getElementById('submitButton');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="iconfont icon-loading animate-spin mr-1"></i> 保存中...';
        submitButton.disabled = true;

        try {
            const form = document.getElementById('skuForm');
            const formData = new FormData(form);

            // 发送请求
            const response = await fetch('{__MPRE__}{:url("appSku/save")}?appId={$app.appId}', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.code === 1) {
                showNotification(data.msg, 'success');
                setTimeout(() => {
                    window.location.href = '{__MPRE__}{:url("appSku/index", ["appId" => $app.appId])}';
                }, 1000);
            } else {
                showNotification(data.msg || '保存失败', 'error');
                resetButton();
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('保存失败，请重试', 'error');
            resetButton();
        }

        function resetButton() {
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 移除现有通知
        const existingNotification = document.getElementById('notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = 'notification';
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                    'bg-blue-500 text-white'
            }`;

        // 添加图标
        const icon = document.createElement('i');
        icon.className = `iconfont mr-2 ${type === 'success' ? 'icon-check-circle' :
                type === 'error' ? 'icon-close-circle' :
                    'icon-info-circle'
            }`;
        notification.appendChild(icon);

        // 添加消息文本
        const text = document.createElement('span');
        text.textContent = message;
        notification.appendChild(text);

        // 添加到页面
        document.body.appendChild(notification);

        // 动画效果
        setTimeout(() => {
            notification.classList.add('translate-y-2', 'opacity-100');
        }, 10);

        // 自动关闭
        setTimeout(() => {
            notification.classList.remove('translate-y-2', 'opacity-100');
            notification.classList.add('-translate-y-2', 'opacity-0');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
</script>

<style>
    /* 添加动画 */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-5px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }

    .animate-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    /* 错误提示动画 */
    @keyframes shake {

        0%,
        100% {
            transform: translateX(0);
        }

        10%,
        30%,
        50%,
        70%,
        90% {
            transform: translateX(-5px);
        }

        20%,
        40%,
        60%,
        80% {
            transform: translateX(5px);
        }
    }

    .animate-shake {
        animation: shake 0.6s ease-in-out;
    }

    /* 输入框焦点效果 */
    .form-group.is-focused label {
        color: #4f46e5;
        /* indigo-600 */
    }

    /* 表单元素过渡效果 */
    input,
    select,
    textarea {
        transition: all 0.2s ease-in-out;
    }

    /* 通知动画 */
    #notification {
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease-in-out;
    }
</style>
{/block}