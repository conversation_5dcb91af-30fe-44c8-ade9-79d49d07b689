<?php
declare (strict_types = 1);

namespace app\validate;

use think\Validate;

/**
 * App验证器
 */
class App extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'appCode' => 'require|alphaDash|unique:app|max:64',
        'title' => 'require|max:128',
        'description' => 'require|max:512',
        'logo' => 'require|max:64',
        'isPrivate' => 'require|in:0,1',
        'isOnline' => 'require|in:0,1',
        'status' => 'require|in:0,1,2',
    ];
    
    /**
     * 错误提示
     */
    protected $message = [
        'appCode.require' => '应用代码不能为空',
        'appCode.alphaDash' => '应用代码只能包含字母、数字、下划线和破折号',
        'appCode.unique' => '应用代码已存在',
        'appCode.max' => '应用代码最多不能超过64个字符',
        'title.require' => '标题不能为空',
        'title.max' => '标题最多不能超过128个字符',
        'description.require' => '说明不能为空',
        'description.max' => '说明最多不能超过512个字符',
        'logo.require' => '徽标不能为空',
        'logo.max' => '徽标路径最多不能超过64个字符',
        'isPrivate.require' => '请选择是否为私有应用',
        'isPrivate.in' => '私有应用状态值不正确',
        'isOnline.require' => '请选择是否上线',
        'isOnline.in' => '上线状态值不正确',
        'status.require' => '状态不能为空',
        'status.in' => '状态值不正确',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'create' => ['appCode', 'title', 'description', 'logo', 'isPrivate', 'isOnline'],
        'edit' => ['title', 'description', 'logo', 'isPrivate', 'isOnline'],
    ];
}
