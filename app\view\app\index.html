{extend name="common/base" /}

{block name="title"}应用列表 - 应用市场管理系统{/block}

{block name="content"}
<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-4">应用管理</h1>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-lg p-5 mb-6 border border-gray-100">
        <div class="flex items-center mb-4">
            <i class="iconfont icon-filter text-indigo-500 text-xl mr-2"></i>
            <h2 class="text-lg font-semibold text-gray-800">筛选条件</h2>
        </div>

        <form action="{__MPRE__}{:url('app/index')}" method="get" id="searchForm">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4">
                <div class="form-group">
                    <label for="title" class="block text-xs font-medium text-gray-700 mb-1">应用标题</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-title text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="title" name="title" value="{$search.title}"
                            placeholder="输入应用标题"
                            class="block w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                    </div>
                </div>

                <div class="form-group">
                    <label for="appCode" class="block text-xs font-medium text-gray-700 mb-1">应用代码</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-code text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="appCode" name="appCode" value="{$search.appCode}"
                            placeholder="输入应用代码"
                            class="block w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                    </div>
                </div>

                <div class="form-group">
                    <label for="isOnline" class="block text-xs font-medium text-gray-700 mb-1">上线状态</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-online text-gray-400 text-xs"></i>
                        </div>
                        <select id="isOnline" name="isOnline"
                            class="block w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                            <option value="">全部状态</option>
                            <option value="1" {if $search.isOnline === '1'}selected{/if}>已上线</option>
                            <option value="0" {if $search.isOnline === '0'}selected{/if}>未上线</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="isPrivate" class="block text-xs font-medium text-gray-700 mb-1">私有状态</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-lock text-gray-400 text-xs"></i>
                        </div>
                        <select id="isPrivate" name="isPrivate"
                            class="block w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                            <option value="">全部类型</option>
                            <option value="1" {if $search.isPrivate === '1'}selected{/if}>私有应用</option>
                            <option value="0" {if $search.isPrivate === '0'}selected{/if}>公有应用</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap justify-between mt-5 pt-4 border-t border-gray-100">
                <div class="flex space-x-2 mb-2 sm:mb-0">
                    <button type="submit"
                        class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-search mr-1"></i> 搜索
                    </button>
                    <button type="button" onclick="resetForm()"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-refresh mr-1"></i> 重置
                    </button>
                </div>

                <a href="{__MPRE__}{:url('app/create')}"
                    class="px-4 py-1.5 text-xs bg-green-600 text-white rounded-md shadow-sm
                    hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2
                    focus:ring-offset-1 focus:ring-green-500 flex items-center">
                    <i class="iconfont icon-add mr-1"></i> 新建应用
                </a>
            </div>
        </form>
    </div>

    <!-- 应用列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {volist name="list" id="item"}
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4">
                <div class="flex items-center mb-4">
                    <img src="//i.cdn.yimenapp.com/{$item.logo}" alt="{$item.title}" class="w-12 h-12 rounded-md mr-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">{$item.title}</h3>
                        <p class="text-sm text-gray-500">AppCode: {$item.appCode}</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4 line-clamp-2">{$item.description}</p>
                <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$item.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                            <i class="iconfont {$item.isOnline ? 'icon-online' : 'icon-offline'} mr-1"></i>
                            {$item.isOnline ? '已上线' : '未上线'}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$item.isPrivate ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'} ml-2">
                            {$item.isPrivate ? '私有应用' : '公有应用'}
                        </span>
                    </div>
                    <span class="text-sm text-gray-500">付费数: {$item.paidCount}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-500">创建时间: {:date('Y-m-d', strtotime($item.createTime))}</span>
                    <div class="flex space-x-2">
                        <a href="{__MPRE__}{:url('app/detail', ['appId' => $item.appId])}" class="text-indigo-600 hover:text-indigo-800">
                            <i class="iconfont icon-detail"></i> 详情
                        </a>
                        <a href="{__MPRE__}{:url('app/edit', ['appId' => $item.appId])}" class="text-yellow-600 hover:text-yellow-800">
                            <i class="iconfont icon-edit"></i> 编辑
                        </a>
                        <a href="javascript:;" onclick="deleteApp({$item.appId})" class="text-red-600 hover:text-red-800">
                            <i class="iconfont icon-delete"></i> 删除
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {/volist}
    </div>

    <!-- 分页 -->
    <div class="mt-6">
        {$list|raw}
    </div>
</div>
{/block}

{block name="script"}
<script>
// 重置筛选表单
function resetForm() {
    const form = document.getElementById('searchForm');
    const inputs = form.querySelectorAll('input[type="text"], select');

    // 清空所有输入框和下拉框
    inputs.forEach(input => {
        input.value = '';
    });

    // 提交表单
    form.submit();
}

// 删除应用
function deleteApp(appId) {
    if (confirm('确定要删除这个应用吗？此操作不可恢复！')) {
        fetch('{__MPRE__}{:url("app/delete")}?appId=' + appId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                // 使用更友好的通知
                showNotification(data.msg, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.msg || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('删除失败，请重试', 'error');
        });
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');
        });
    });
});
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 输入框焦点效果 */
.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}
</style>
{/block}
