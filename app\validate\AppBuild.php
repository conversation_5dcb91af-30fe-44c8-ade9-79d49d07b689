<?php
declare (strict_types = 1);

namespace app\validate;

use think\Validate;

/**
 * AppBuild验证器
 */
class AppBuild extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'appId' => 'require|number',
        'versionCode' => 'require|number|gt:0|unique:app_build,versionCode^appId',
        'versionName' => 'require|max:16',
        'description' => 'require|max:512',
        'appConfig' => 'require|json',
        'hasPackage' => 'require|in:0,1',
        'packageUploaded' => 'require|in:0,1',
        'status' => 'require|in:0,1,2',
    ];
    
    /**
     * 错误提示
     */
    protected $message = [
        'appId.require' => '应用ID不能为空',
        'appId.number' => '应用ID必须为数字',
        'versionCode.require' => '版本号不能为空',
        'versionCode.number' => '版本号必须为数字',
        'versionCode.gt' => '版本号必须大于0',
        'versionCode.unique' => '该应用下已存在相同版本号',
        'versionName.require' => '版本名称不能为空',
        'versionName.max' => '版本名称最多不能超过16个字符',
        'description.require' => '说明不能为空',
        'description.max' => '说明最多不能超过512个字符',
        'appConfig.require' => '应用配置不能为空',
        'appConfig.json' => '应用配置必须为有效的JSON格式',
        'hasPackage.require' => '请选择是否有离线前端包',
        'hasPackage.in' => '离线前端包状态值不正确',
        'packageUploaded.require' => '请选择前端离线包是否已上传',
        'packageUploaded.in' => '前端离线包上传状态值不正确',
        'status.require' => '状态不能为空',
        'status.in' => '状态值不正确',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'create' => ['appId', 'versionCode', 'versionName', 'description', 'appConfig', 'hasPackage', 'packageUploaded'],
        'edit' => ['appId', 'versionName', 'description', 'appConfig', 'hasPackage', 'packageUploaded'],
    ];
}
