{extend name="common/base" /}

{block name="title"}{$app.title} - 应用包管理 - 应用市场管理系统{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 p-2 rounded-lg shadow-md mr-3">
                        <i class="iconfont icon-package text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800">应用包管理</h2>
                        <p class="text-sm text-gray-500 mt-1">管理应用的版本和配置</p>
                    </div>
                </div>
                <a href="{__MPRE__}{:url('appBuild/create', ['appId' => $app.appId])}"
                   class="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-md
                   hover:from-green-600 hover:to-green-700 shadow-sm transition-all duration-300
                   transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500
                   flex items-center">
                    <i class="iconfont icon-add mr-1"></i> 新建应用包
                </a>
            </div>

            <!-- 搜索和筛选 -->
            <div class="mb-8">
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-1"></div>
                    <div class="p-5">
                        <div class="flex items-center mb-4">
                            <i class="iconfont icon-filter text-indigo-500 text-lg mr-2"></i>
                            <h3 class="text-sm font-semibold text-gray-700">筛选条件</h3>
                            <div class="ml-auto">
                                <button type="button" id="toggleFilterBtn" class="text-xs text-indigo-600 hover:text-indigo-800 flex items-center">
                                    <i class="iconfont icon-down mr-1 transform transition-transform duration-300" id="toggleFilterIcon"></i>
                                    <span id="toggleFilterText">收起筛选</span>
                                </button>
                            </div>
                        </div>

                        <form id="filterForm" action="{__MPRE__}{:url('appBuild/index', ['appId' => $app.appId])}" method="get" class="transition-all duration-300">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                                <div class="form-group">
                                    <label for="versionCode" class="block text-xs font-medium text-gray-700 mb-1">版本号</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="iconfont icon-version text-gray-400 text-xs"></i>
                                        </div>
                                        <input type="text" id="versionCode" name="versionCode" value="{$search.versionCode}"
                                            placeholder="搜索版本号"
                                            class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                            hover:border-indigo-300 transition-colors duration-200">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="versionName" class="block text-xs font-medium text-gray-700 mb-1">版本名称</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="iconfont icon-tag text-gray-400 text-xs"></i>
                                        </div>
                                        <input type="text" id="versionName" name="versionName" value="{$search.versionName}"
                                            placeholder="搜索版本名称"
                                            class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                            hover:border-indigo-300 transition-colors duration-200">
                                    </div>
                                </div>

                                <div class="form-group md:flex md:items-end">
                                    <div class="hidden md:block w-full">
                                        <label class="block text-xs font-medium text-gray-700 mb-1 opacity-0">操作</label>
                                        <div class="flex space-x-2">
                                            <button type="submit"
                                                class="flex-1 px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                                                hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                                focus:ring-offset-1 focus:ring-indigo-500 flex items-center justify-center">
                                                <i class="iconfont icon-search mr-1"></i> 搜索
                                            </button>
                                            <button type="button" onclick="resetForm()"
                                                class="flex-1 px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                                                hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                                                focus:ring-offset-1 focus:ring-gray-400 flex items-center justify-center">
                                                <i class="iconfont icon-refresh mr-1"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 移动端按钮 -->
                            <div class="mt-4 flex space-x-2 md:hidden">
                                <button type="submit"
                                    class="flex-1 px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                                    hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                    focus:ring-offset-1 focus:ring-indigo-500 flex items-center justify-center">
                                    <i class="iconfont icon-search mr-1"></i> 搜索
                                </button>
                                <button type="button" onclick="resetForm()"
                                    class="flex-1 px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                                    hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                                    focus:ring-offset-1 focus:ring-gray-400 flex items-center justify-center">
                                    <i class="iconfont icon-refresh mr-1"></i> 重置
                                </button>
                            </div>

                            <input type="hidden" name="appId" value="{$app.appId}">
                        </form>
                    </div>
                </div>
            </div>

            <!-- 应用包列表 -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6">
                <div class="flex items-center p-4 border-b border-gray-200 bg-gray-50">
                    <i class="iconfont icon-list text-indigo-500 text-lg mr-2"></i>
                    <h3 class="text-sm font-semibold text-gray-700">应用包列表</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">说明</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">离线包</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {volist name="list" id="item"}
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 mr-2 text-indigo-500">
                                            <i class="iconfont icon-version"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">v{$item.versionName}</div>
                                            <div class="text-xs text-gray-500">版本号: {$item.versionCode}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="text-xs text-gray-900 line-clamp-2">{$item.description}</div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {$item.hasPackage ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                                        <i class="iconfont {$item.hasPackage ? 'icon-package' : 'icon-close'} mr-1 text-xs"></i>
                                        {$item.hasPackage ? '有离线包' : '无离线包'}
                                    </span>
                                    {if $item.hasPackage}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {$item.packageUploaded ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'} ml-1">
                                        <i class="iconfont {$item.packageUploaded ? 'icon-check' : 'icon-warning'} mr-1 text-xs"></i>
                                        {$item.packageUploaded ? '已上传' : '未上传'}
                                    </span>
                                    {/if}
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                        {switch name="item.status"}
                                            {case value="0"}bg-gray-100 text-gray-800{/case}
                                            {case value="1"}bg-yellow-100 text-yellow-800{/case}
                                            {case value="2"}bg-green-100 text-green-800{/case}
                                            {default /}bg-gray-100 text-gray-800
                                        {/switch}
                                    ">
                                        <i class="iconfont
                                            {switch name="item.status"}
                                                {case value="0"}icon-edit{/case}
                                                {case value="1"}icon-time{/case}
                                                {case value="2"}icon-check{/case}
                                                {default /}icon-question
                                            {/switch}
                                        mr-1 text-xs"></i>
                                        {switch name="item.status"}
                                            {case value="0"}编辑中{/case}
                                            {case value="1"}审核中{/case}
                                            {case value="2"}审核通过{/case}
                                            {default /}未知状态
                                        {/switch}
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">
                                    <div class="flex items-center">
                                        <i class="iconfont icon-time text-gray-400 mr-1"></i>
                                        {:date('Y-m-d H:i', strtotime($item.createTime))}
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-right text-xs font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <a href="{__MPRE__}{:url('appBuild/edit', ['appId' => $app.appId, 'appBuildId' => $item.appBuildId])}"
                                           class="px-2 py-1 bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100
                                           transition-colors duration-200 flex items-center">
                                            <i class="iconfont icon-edit mr-1"></i> 编辑
                                        </a>
                                        <button type="button" class="delete-build-btn px-2 py-1 bg-red-50 text-red-600 rounded hover:bg-red-100
                                           transition-colors duration-200 flex items-center" data-id="{$item.appBuildId}">
                                            <i class="iconfont icon-delete mr-1"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {/volist}

                            {empty name="list"}
                            <tr>
                                <td colspan="6" class="px-4 py-6 text-center text-sm text-gray-500">
                                    <div class="flex flex-col items-center justify-center py-6">
                                        <i class="iconfont icon-empty text-gray-300 text-4xl mb-2"></i>
                                        <p>暂无应用包数据</p>
                                    </div>
                                </td>
                            </tr>
                            {/empty}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <div class="mt-6 flex justify-center">
                <div class="pagination-container">
                    {$list|raw}
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化筛选表单
    initFilterForm();

    // 初始化表单元素
    initFormElements();

    // 初始化删除按钮
    initDeleteButtons();
});

// 初始化筛选表单
function initFilterForm() {
    const toggleBtn = document.getElementById('toggleFilterBtn');
    const filterForm = document.getElementById('filterForm');
    const toggleIcon = document.getElementById('toggleFilterIcon');
    const toggleText = document.getElementById('toggleFilterText');

    if (toggleBtn && filterForm) {
        // 检查是否有筛选条件
        const hasFilters = checkHasFilters();

        // 如果没有筛选条件，默认收起筛选表单
        if (!hasFilters) {
            toggleFilter();
        }

        // 添加点击事件
        toggleBtn.addEventListener('click', toggleFilter);
    }

    function toggleFilter() {
        const formContent = filterForm.querySelector('.grid');
        const mobileButtons = filterForm.querySelector('.mt-4.flex');

        if (formContent.classList.contains('hidden')) {
            // 展开筛选表单
            formContent.classList.remove('hidden');
            if (mobileButtons) mobileButtons.classList.remove('hidden');
            toggleIcon.classList.remove('rotate-180');
            toggleText.textContent = '收起筛选';
        } else {
            // 收起筛选表单
            formContent.classList.add('hidden');
            if (mobileButtons) mobileButtons.classList.add('hidden');
            toggleIcon.classList.add('rotate-180');
            toggleText.textContent = '展开筛选';
        }
    }

    // 检查是否有筛选条件
    function checkHasFilters() {
        const versionCode = document.getElementById('versionCode').value;
        const versionName = document.getElementById('versionName').value;

        return versionCode || versionName;
    }
}

// 初始化表单元素
function initFormElements() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], input[type="number"]');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');
        });
    });
}

// 重置筛选表单
function resetForm() {
    const form = document.getElementById('filterForm');
    if (form) {
        // 清空所有输入框
        const inputs = form.querySelectorAll('input[type="text"], input[type="number"]');
        inputs.forEach(input => {
            input.value = '';
        });

        // 提交表单
        form.submit();
    }
}

// 初始化删除按钮
function initDeleteButtons() {
    const deleteButtons = document.querySelectorAll('.delete-build-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const appBuildId = this.getAttribute('data-id');
            deleteBuild(appBuildId);
        });
    });
}

// 删除应用包
async function deleteBuild(appBuildId) {
    // 显示确认对话框
    if (confirm('确定要删除这个应用包吗？此操作不可恢复！')) {
        try {
            // 显示加载状态
            showNotification('正在删除...', 'info');

            // 发送请求
            const response = await fetch('{__MPRE__}{:url("appBuild/delete")}?appId={$app.appId}&appBuildId=' + appBuildId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.code === 1) {
                showNotification(data.msg, 'success');

                // 找到对应的行并添加淡出效果
                const row = document.querySelector(`.delete-build-btn[data-id="${appBuildId}"]`).closest('tr');
                if (row) {
                    row.style.transition = 'all 0.5s ease-out';
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(20px)';
                }

                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.msg || '删除失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('删除失败，请重试', 'error');
        }
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 输入框焦点效果 */
.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select, textarea {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 表格行悬停效果 */
tbody tr {
    transition: background-color 0.15s ease-in-out;
}

/* 美化滚动条 */
.overflow-x-auto::-webkit-scrollbar {
    height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

/* 美化分页 */
.pagination-container ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.25rem;
}

.pagination-container li {
    display: inline-block;
}

.pagination-container li span,
.pagination-container li a {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2rem;
    height: 2rem;
    padding: 0 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.pagination-container li a {
    color: #4f46e5;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
}

.pagination-container li a:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.pagination-container li.active span {
    color: white;
    background-color: #4f46e5;
    border: 1px solid #4338ca;
}

.pagination-container li.disabled span {
    color: #9ca3af;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    cursor: not-allowed;
}
</style>
{/block}
