<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * AppRelease模型
 */
class AppRelease extends Model
{
    // 设置表名
    protected $name = 'app_release';

    // 设置主键
    protected $pk = 'appReleaseId';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 设置创建时间字段
    protected $createTime = 'createTime';

    // 设置更新时间字段
    protected $updateTime = false;

    /**
     * 获取所属运行时环境
     */
    public function runtime()
    {
        return $this->belongsTo(AppRuntime::class, 'appRuntimeId', 'appRuntimeId');
    }

    /**
     * 获取所属应用包
     */
    public function build()
    {
        return $this->belongsTo(AppBuild::class, 'appBuildId', 'appBuildId');
    }

    /**
     * 搜索器：按描述搜索
     */
    public function searchDescriptionAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('description', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按上线状态搜索
     */
    public function searchIsOnlineAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('isOnline', $value);
        }
    }

    /**
     * 搜索器：按应用包ID搜索
     */
    public function searchAppBuildIdAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('appBuildId', $value);
        }
    }
}
