<?php

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * AppSku验证器
 */
class AppSku extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|max:64',
        'description' => 'max:256',
        'salePrice' => 'require|float|egt:0',
        'fullPrice' => 'require|float|egt:0',
        'sort' => 'require|number|egt:0',
        'runtimeIds' => 'require|array',
        'isOnline' => 'require|in:0,1',
    ];

    /**
     * 错误提示
     */
    protected $message = [
        'name.require' => '名称不能为空',
        'name.max' => '名称最多不能超过64个字符',
        'description.max' => '说明最多不能超过256个字符',
        'salePrice.require' => '售价不能为空',
        'salePrice.float' => '售价必须为数字',
        'salePrice.egt' => '售价必须大于等于0',
        'fullPrice.require' => '原价不能为空',
        'fullPrice.float' => '原价必须为数字',
        'fullPrice.egt' => '原价必须大于等于0',
        'sort.require' => '排序不能为空',
        'sort.number' => '排序必须为数字',
        'sort.egt' => '排序必须大于等于0',
        'runtimeIds.require' => '请选择至少一个运行时环境',
        'runtimeIds.array' => '运行时环境ID必须为数组',
        'isOnline.require' => '请选择是否上线',
        'isOnline.in' => '上线状态值不正确',

    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'create' => ['name', 'description', 'salePrice', 'fullPrice', 'sort', 'runtimeIds', 'isOnline'],
        'edit' => ['name', 'description', 'salePrice', 'fullPrice', 'sort', 'runtimeIds', 'isOnline'],
    ];
}
