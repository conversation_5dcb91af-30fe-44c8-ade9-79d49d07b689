<?php
declare (strict_types = 1);

namespace app\validate;

use think\Validate;

/**
 * AppRuntime验证器
 */
class AppRuntime extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'appId' => 'require|number',
        'runtimeId' => 'require|number',
        'requiredPluginIds' => 'max:1024',
        'demoLink' => 'url|max:128',
        'content' => 'max:65535',
        'isOnline' => 'require|in:0,1',
        'status' => 'require|in:0,1,2',
    ];
    
    /**
     * 错误提示
     */
    protected $message = [
        'appId.require' => '应用ID不能为空',
        'appId.number' => '应用ID必须为数字',
        'runtimeId.require' => '运行时环境ID不能为空',
        'runtimeId.number' => '运行时环境ID必须为数字',
        'requiredPluginIds.max' => '所需原生插件ID最多不能超过1024个字符',
        'demoLink.url' => '演示链接必须为有效的URL',
        'demoLink.max' => '演示链接最多不能超过128个字符',
        'content.max' => '详情内容过长',
        'isOnline.require' => '请选择是否上线',
        'isOnline.in' => '上线状态值不正确',
        'status.require' => '状态不能为空',
        'status.in' => '状态值不正确',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'edit' => ['appId', 'runtimeId', 'requiredPluginIds', 'demoLink', 'content', 'isOnline'],
    ];
}
