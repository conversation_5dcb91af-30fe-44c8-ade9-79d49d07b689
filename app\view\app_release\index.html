{extend name="common/base" /}

{block name="title"}{$app.title} - {$appRuntime.runtimeId}环境发布列表 - 应用市场管理系统{/block}

{block name="style"}
<style>
/* 表格滚动样式 */
.max-h-96 {
    max-height: 24rem;
}
.sticky {
    position: sticky;
}
.top-0 {
    top: 0;
}
.z-10 {
    z-index: 10;
}
thead.sticky {
    background-color: #f9fafb;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 表单美化样式 */
.form-group {
    position: relative;
    margin-bottom: 0.5rem;
}

.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 输入框动画 */
input, select, textarea {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 加载动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 错误提示动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
    animation: shake 0.6s ease-in-out;
}

/* 表格行悬停效果 */
tbody tr {
    transition: background-color 0.15s ease-in-out;
}

tbody tr:hover {
    background-color: #f9fafb;
}

/* 美化滚动条 */
.max-h-96::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.max-h-96::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

/* 卡片阴影效果 */
.card-shadow {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
    transition: box-shadow 0.3s ease-in-out;
}

.card-shadow:hover {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06);
}
</style>
{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="iconfont {$runtimeName} text-indigo-600 mr-2"></i>
                    {$runtimeName}环境配置
                </h2>
                <div class="flex space-x-2">
                    <a href="{__MPRE__}{:url('app/detail', ['appId' => $app.appId])}" class="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs flex items-center">
                        <i class="iconfont icon-back mr-1"></i> 返回应用
                    </a>
                </div>
            </div>

            <!-- 选项卡 -->
            <div class="border-b border-gray-200 mb-6">
                <ul class="flex flex-wrap -mb-px">
                    <li class="mr-2">
                        <a href="{__MPRE__}{:url('appRuntime/edit', ['appId' => $app.appId, 'runtimeId' => $runtimeId, 'tab' => 'config'])}"
                           class="inline-flex items-center py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent transition-colors duration-200">
                            <i class="iconfont icon-setting mr-1"></i> 配置
                        </a>
                    </li>
                    
                    <li class="mr-2">
                        <a href="{__MPRE__}{:url('appRelease/index', ['appId' => $app.appId, 'runtimeId' => $runtimeId])}"
                           class="inline-flex items-center py-2 px-4 text-sm font-medium text-indigo-600 border-b-2 border-indigo-600 transition-colors duration-200">
                            <i class="iconfont icon-release mr-1"></i> 发布列表
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 搜索和筛选 -->
            <div class="bg-white rounded-lg p-4 mb-6 border border-gray-100 card-shadow">
                <div class="flex items-center mb-3">
                    <i class="iconfont icon-filter text-indigo-500 text-lg mr-2"></i>
                    <h3 class="text-sm font-semibold text-gray-700">筛选条件</h3>
                </div>

                <form action="{__MPRE__}{:url('appRelease/index')}" method="get" id="searchForm">
                    <input type="hidden" name="appId" value="{$app.appId}">
                    <input type="hidden" name="runtimeId" value="{$runtimeId}">

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="form-group">
                            <label for="description" class="block text-xs font-medium text-gray-700 mb-1">描述</label>
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-description text-gray-400 text-xs"></i>
                                </div>
                                <input type="text" id="description" name="description" value="{$search.description}"
                                    placeholder="搜索描述内容"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="appBuildId" class="block text-xs font-medium text-gray-700 mb-1">应用包</label>
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-package text-gray-400 text-xs"></i>
                                </div>
                                <select id="appBuildId" name="appBuildId"
                                    class="w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                                    <option value="">全部应用包</option>
                                    {volist name="builds" id="build"}
                                    <option value="{$build.appBuildId}" {if $search.appBuildId == $build.appBuildId}selected{/if}>v{$build.versionName} (#{$build.versionCode})</option>
                                    {/volist}
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-down text-gray-400 text-xs"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="isOnline" class="block text-xs font-medium text-gray-700 mb-1">状态</label>
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-status text-gray-400 text-xs"></i>
                                </div>
                                <select id="isOnline" name="isOnline"
                                    class="w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                                    <option value="">全部状态</option>
                                    <option value="1" {if $search.isOnline === '1'}selected{/if}>已上线</option>
                                    <option value="0" {if $search.isOnline === '0'}selected{/if}>未上线</option>
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-down text-gray-400 text-xs"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-wrap justify-between mt-4 pt-3 border-t border-gray-100">
                        <div class="flex space-x-2 mb-2 sm:mb-0">
                            <button type="submit"
                                class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                                hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                                <i class="iconfont icon-search mr-1"></i> 搜索
                            </button>
                            <button type="button" onclick="resetForm()"
                                class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                                hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                                focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                                <i class="iconfont icon-refresh mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 新建发布表单 -->
            <div class="bg-white rounded-lg border border-gray-200 p-5 mb-6 card-shadow">
                <div class="flex items-center mb-4">
                    <i class="iconfont icon-add text-green-600 text-lg mr-2"></i>
                    <h3 class="text-lg font-semibold text-gray-700">新建发布</h3>
                </div>

                <form id="releaseForm" class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div class="form-group">
                        <label for="newAppBuildId" class="block text-xs font-medium text-gray-700 mb-1">选择应用包 <span class="text-red-500">*</span></label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="iconfont icon-package text-gray-400 text-xs"></i>
                            </div>
                            <select id="newAppBuildId" name="appBuildId" required
                                class="w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                                focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                hover:border-indigo-300 transition-colors duration-200">
                                <option value="">请选择应用包</option>
                                {volist name="builds" id="build"}
                                <option value="{$build.appBuildId}">v{$build.versionName} (#{$build.versionCode})</option>
                                {/volist}
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="iconfont icon-down text-gray-400 text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="newIsOnline" class="block text-xs font-medium text-gray-700 mb-1">上线状态 <span class="text-red-500">*</span></label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="iconfont icon-status text-gray-400 text-xs"></i>
                            </div>
                            <select id="newIsOnline" name="isOnline" required
                                class="w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                                focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                hover:border-indigo-300 transition-colors duration-200">
                                <option value="1">上线</option>
                                <option value="0">不上线</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="iconfont icon-down text-gray-400 text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <div class="form-group md:col-span-3">
                        <label for="newDescription" class="block text-xs font-medium text-gray-700 mb-1">描述 <span class="text-red-500">*</span></label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute top-2 left-3 text-gray-400 text-xs">
                                <i class="iconfont icon-description"></i>
                            </div>
                            <textarea id="newDescription" name="description" required rows="3"
                                placeholder="请输入发布描述信息"
                                class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                hover:border-indigo-300 transition-colors duration-200"></textarea>
                        </div>
                    </div>

                    <div class="md:col-span-3 flex justify-end pt-3 border-t border-gray-100">
                        <button type="button" id="submitButton" onclick="submitReleaseForm()"
                            class="px-4 py-1.5 text-xs bg-green-600 text-white rounded-md shadow-sm
                            hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2
                            focus:ring-offset-1 focus:ring-green-500 flex items-center">
                            <i class="iconfont icon-add mr-1"></i> 创建发布
                        </button>
                    </div>
                </form>
            </div>

            <!-- 发布列表 -->
            <div class="border rounded-lg overflow-hidden shadow-sm">
                <div class="flex items-center p-3 bg-gray-50 border-b border-gray-200">
                    <i class="iconfont icon-list text-indigo-500 text-lg mr-2"></i>
                    <h3 class="text-sm font-semibold text-gray-700">发布列表</h3>
                </div>

                <div class="overflow-x-auto">
                    <div class="max-h-96 overflow-y-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用包</th>
                                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {volist name="list" id="item"}
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 mr-2 text-indigo-500">
                                                <i class="iconfont icon-package"></i>
                                            </div>
                                            <div>
                                                <div class="text-xs font-medium text-gray-900">v{$item.build.versionName}</div>
                                                <div class="text-xs text-gray-500">版本号: {$item.build.versionCode}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="text-xs text-gray-900 line-clamp-2">{$item.description}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {$item.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                            <i class="iconfont {$item.isOnline ? 'icon-online' : 'icon-offline'} mr-1 text-xs"></i>
                                            {$item.isOnline ? '已上线' : '未上线'}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">
                                        {$item.createTime}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-right text-xs font-medium">
                                        <div class="flex justify-end space-x-2">
                                            {if $item.isOnline == 0}
                                            <button type="button" onclick="updateReleaseStatus({$item.appReleaseId}, 1)"
                                                class="inline-flex items-center px-2 py-1 bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100 transition-colors duration-200">
                                                <i class="iconfont icon-online mr-1"></i> 上线
                                            </button>
                                            {else}
                                            <button type="button" onclick="updateReleaseStatus({$item.appReleaseId}, 0)"
                                                class="inline-flex items-center px-2 py-1 bg-yellow-50 text-yellow-600 rounded hover:bg-yellow-100 transition-colors duration-200">
                                                <i class="iconfont icon-offline mr-1"></i> 下线
                                            </button>
                                            {/if}
                                            <button type="button" onclick="deleteRelease({$item.appReleaseId})"
                                                class="inline-flex items-center px-2 py-1 bg-red-50 text-red-600 rounded hover:bg-red-100 transition-colors duration-200">
                                                <i class="iconfont icon-delete mr-1"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                                {if $list->isEmpty()}
                                <tr>
                                    <td colspan="5" class="px-4 py-4 text-center text-sm text-gray-500">
                                        <div class="flex flex-col items-center justify-center py-6">
                                            <i class="iconfont icon-empty text-gray-300 text-4xl mb-2"></i>
                                            <p>暂无发布数据</p>
                                        </div>
                                    </td>
                                </tr>
                                {/if}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="mt-6">
                {$list|raw}
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表单元素
    initFormElements();
});

// 初始化表单元素
function initFormElements() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], textarea, select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');

            // 简单验证（如果是必填字段）
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('border-red-500');
            } else {
                this.classList.remove('border-red-500');
            }
        });
    });
}

// 重置筛选表单
function resetForm() {
    const form = document.getElementById('searchForm');
    const inputs = form.querySelectorAll('input[type="text"], select');

    // 清空所有输入框和下拉框
    inputs.forEach(input => {
        input.value = '';
    });

    // 提交表单
    form.submit();
}

// 验证表单
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('border-red-500');

            // 添加抖动效果
            field.classList.add('animate-shake');
            setTimeout(() => {
                field.classList.remove('animate-shake');
            }, 600);

            // 添加错误提示
            const fieldGroup = field.closest('.form-group');
            if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                const errorMsg = document.createElement('p');
                errorMsg.className = 'error-message text-red-500 text-xs mt-1';
                errorMsg.textContent = '此字段不能为空';
                fieldGroup.appendChild(errorMsg);
            }

            isValid = false;
        } else {
            field.classList.remove('border-red-500');

            // 移除错误提示
            const fieldGroup = field.closest('.form-group');
            const errorMsg = fieldGroup?.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.remove();
            }
        }
    });

    // 如果有错误，滚动到第一个错误字段
    if (!isValid) {
        const firstErrorField = form.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorField.focus();
        }
    }

    return isValid;
}

// 提交发布表单
async function submitReleaseForm() {
    const form = document.getElementById('releaseForm');

    // 验证表单
    if (!validateForm(form)) {
        return;
    }

    const formData = new FormData(form);

    // 显示加载状态
    const submitButton = document.getElementById('submitButton');
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="iconfont icon-loading animate-spin mr-1"></i> 创建中...';
    submitButton.disabled = true;

    try {
        // 发送请求
        const response = await fetch('{__MPRE__}{:url("appRelease/save")}?appId={$app.appId}&runtimeId={$runtimeId}', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '创建失败', 'error');
            resetButton();
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('创建失败，请重试', 'error');
        resetButton();
    }

    function resetButton() {
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    }
}

// 更新发布状态
async function updateReleaseStatus(appReleaseId, isOnline) {
    // 使用更友好的确认对话框
    if (!confirm(isOnline ? '确定要上线此发布吗？' : '确定要下线此发布吗？')) {
        return;
    }

    try {
        // 显示加载状态
        showNotification(isOnline ? '正在上线...' : '正在下线...', 'info');

        // 发送请求
        const response = await fetch('{__MPRE__}{:url("appRelease/updateStatus")}?appId={$app.appId}&runtimeId={$runtimeId}&appReleaseId=' + appReleaseId + '&isOnline=' + isOnline, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 删除发布
async function deleteRelease(appReleaseId) {
    // 使用更友好的确认对话框
    if (!confirm('确定要删除此发布吗？此操作不可恢复！')) {
        return;
    }

    try {
        // 显示加载状态
        showNotification('正在删除...', 'info');

        // 发送请求
        const response = await fetch('{__MPRE__}{:url("appRelease/delete")}?appId={$app.appId}&runtimeId={$runtimeId}&appReleaseId=' + appReleaseId, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '删除失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('删除失败，请重试', 'error');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>
{/block}
