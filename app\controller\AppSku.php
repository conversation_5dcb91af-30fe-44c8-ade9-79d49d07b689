<?php

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\AppSku as AppSkuModel;
use app\model\AppRuntime as AppRuntimeModel;
use app\validate\AppSku as AppSkuValidate;
use think\exception\ValidateException;
use think\facade\Db;

/**
 * AppSku控制器
 */
class AppSku extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\Auth',
        'app\\middleware\\AppCheck',
    ];

    /**
     * SKU列表
     */
    public function index(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取搜索参数
        $search = [
            'name' => $request->param('name', ''),
            'priceRange' => [
                $request->param('minPrice', ''),
                $request->param('maxPrice', ''),
            ],
        ];

        // 查询SKU列表
        $list = AppSkuModel::getSkusByAppId($app->appId, $search)
            ->order('sort', 'asc')
            ->paginate([
                'list_rows' => 10,
                'query' => $request->param(),
            ]);

        // 模板赋值
        View::assign([
            'app' => $app,
            'list' => $list,
            'search' => $search,
            'active' => 'sku',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 创建SKU页面
     */
    public function create(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境列表
        $runtimes = AppRuntimeModel::where('appId', $app->appId)
            ->select();

        // 模板赋值
        View::assign([
            'app' => $app,
            'runtimes' => $runtimes,
            'active' => 'sku',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 保存SKU
     */
    public function save(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取参数
        $data = $request->post();

        // 获取运行时环境ID列表
        $runtimeIds = $request->post('runtimeIds/a', []);
        $data['runtimeIds'] = $runtimeIds;

        // 验证数据
        try {
            validate(AppSkuValidate::class)
                ->scene('create')
                ->check($data);
        } catch (ValidateException $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 创建SKU
            $appSku = new AppSkuModel();
            $appSku->save([
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'salePrice' => $data['salePrice'],
                'fullPrice' => $data['fullPrice'],
                'sort' => $data['sort'],
                'paidCount' => 0,
                'isOnline' => $data['isOnline']
            ]);

            // 关联运行时环境
            if (!empty($runtimeIds)) {
                $skuRuntimeData = [];
                foreach ($runtimeIds as $runtimeId) {
                    // 查询运行时环境ID
                    $appRuntime = AppRuntimeModel::where('appId', $app->appId)
                        ->where('runtimeId', $runtimeId)
                        ->find();

                    if ($appRuntime) {
                        $skuRuntimeData[] = [
                            'appSkuId' => $appSku->appSkuId,
                            'appRuntimeId' => $appRuntime->appRuntimeId,
                        ];
                    }
                }

                if (!empty($skuRuntimeData)) {
                    Db::name('app_sku_runtime')->insertAll($skuRuntimeData);
                }
            }

            // 提交事务
            Db::commit();

            // 返回结果
            return json(['code' => 1, 'msg' => '创建成功', 'data' => ['appSkuId' => $appSku->appSkuId]]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            // 返回错误
            return json(['code' => 0, 'msg' => '创建失败：' . $e->getMessage()]);
        }
    }

    /**
     * 编辑SKU页面
     */
    public function edit(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取SKU ID
        $appSkuId = $request->param('appSkuId');

        // 查询SKU信息
        $appSku = AppSkuModel::where('appSkuId', $appSkuId)->find();

        // 检查SKU是否属于当前应用
        if (!$appSku || !AppSkuModel::checkSkuBelongsToApp($appSkuId, $app->appId)) {
            return $this->error('SKU不存在');
        }

        // 获取运行时环境列表
        $runtimes = AppRuntimeModel::where('appId', $app->appId)
            ->select();

        // 获取已选择的运行时环境ID列表
        $selectedRuntimeIds = Db::name('app_sku_runtime')
            ->where('appSkuId', $appSkuId)
            ->column('appRuntimeId');

        // 获取已选择的运行时环境的runtimeId列表
        $selectedRuntimeIds = AppRuntimeModel::whereIn('appRuntimeId', $selectedRuntimeIds)
            ->column('runtimeId');

        // 模板赋值
        View::assign([
            'app' => $app,
            'appSku' => $appSku,
            'runtimes' => $runtimes,
            'selectedRuntimeIds' => $selectedRuntimeIds,
            'active' => 'sku',

        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 更新SKU
     */
    public function update(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取SKU ID
        $appSkuId = $request->param('appSkuId');

        // 查询SKU信息
        $appSku = AppSkuModel::where('appSkuId', $appSkuId)->find();

        // 检查SKU是否属于当前应用
        if (!$appSku || !AppSkuModel::checkSkuBelongsToApp($appSkuId, $app->appId)) {
            return json(['code' => 0, 'msg' => 'SKU不存在']);
        }

        // 获取参数
        $data = $request->post();

        // 获取运行时环境ID列表
        $runtimeIds = $request->post('runtimeIds/a', []);
        $data['runtimeIds'] = $runtimeIds;

        // 验证数据
        try {
            validate(AppSkuValidate::class)
                ->scene('edit')
                ->check($data);
        } catch (ValidateException $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新SKU
            $appSku->save([
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'salePrice' => $data['salePrice'],
                'fullPrice' => $data['fullPrice'],
                'sort' => $data['sort'],
                'isOnline' => $data['isOnline']
            ]);

            // 删除原有关联
            Db::name('app_sku_runtime')
                ->where('appSkuId', $appSkuId)
                ->delete();

            // 重新关联运行时环境
            if (!empty($runtimeIds)) {
                $skuRuntimeData = [];
                foreach ($runtimeIds as $runtimeId) {
                    // 查询运行时环境ID
                    $appRuntime = AppRuntimeModel::where('appId', $app->appId)
                        ->where('runtimeId', $runtimeId)
                        ->find();

                    if ($appRuntime) {
                        $skuRuntimeData[] = [
                            'appSkuId' => $appSku->appSkuId,
                            'appRuntimeId' => $appRuntime->appRuntimeId,
                        ];
                    }
                }

                if (!empty($skuRuntimeData)) {
                    Db::name('app_sku_runtime')->insertAll($skuRuntimeData);
                }
            }

            // 提交事务
            Db::commit();

            // 返回结果
            return json(['code' => 1, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            // 返回错误
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除SKU
     */
    public function delete(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取SKU ID
        $appSkuId = $request->param('appSkuId');

        // 查询SKU信息
        $appSku = AppSkuModel::where('appSkuId', $appSkuId)->find();

        // 检查SKU是否属于当前应用
        if (!$appSku || !AppSkuModel::checkSkuBelongsToApp($appSkuId, $app->appId)) {
            return json(['code' => 0, 'msg' => 'SKU不存在']);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 删除关联
            Db::name('app_sku_runtime')
                ->where('appSkuId', $appSkuId)
                ->delete();

            // 删除SKU
            $appSku->delete();

            // 提交事务
            Db::commit();

            // 返回结果
            return json(['code' => 1, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            // 返回错误
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
}