{extend name="common/base" /}

{block name="title"}应用市场 - 发现优质应用{/block}

{block name="style"}
<style>
    /* 卡片悬停效果 */
    .app-card {
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }
    .app-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #4f46e5;
    }
    /* 标签动画 */
    .app-tag {
        transition: all 0.2s ease;
    }
    .app-tag:hover {
        transform: scale(1.05);
    }
    /* 图片加载动画 */
    @keyframes imgLoading {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    .app-logo {
        animation: imgLoading 0.5s ease-in-out;
    }
    /* 响应式调整 */
    @media (max-width: 640px) {
        .app-description {
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }
    /* 横幅背景 */
    .market-banner {
        background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%234f46e5" fill-opacity="0.1" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
        background-position: bottom;
        background-repeat: no-repeat;
        background-size: 100% 50%;
    }
    /* 分类标签 */
    .category-tab {
        transition: all 0.2s ease;
        cursor: pointer;
        border-bottom: 2px solid transparent;
    }
    .category-tab.active {
        border-color: #4f46e5;
        color: #4f46e5;
    }
    .category-tab:hover:not(.active) {
        border-color: #a5b4fc;
        color: #6366f1;
    }
    /* 购买按钮 */
    .buy-button {
        transition: all 0.3s ease;
    }
    .buy-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    /* 价格标签 */
    .price-tag {
        position: absolute;
        top: 10px;
        right: 10px;
        background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        transform: rotate(5deg);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{/block}

{block name="content"}
<div class="mb-6">
    <!-- 市场头部 - 增强版横幅 -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg shadow-lg p-6 mb-8 market-banner relative overflow-hidden">
        <div class="max-w-5xl mx-auto">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="mb-6 md:mb-0">
                    <h1 class="text-3xl md:text-4xl font-bold mb-2">应用市场</h1>
                    <p class="text-indigo-100 mb-4 text-lg">发现并使用各种优质应用，提升您的工作效率</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        {volist name="ymConfig.runtime" id="runtimeGroup"}
                        <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
                            <i class="iconfont {$runtimeGroup.icon} mr-1"></i>
                            {$runtimeGroup.label}
                        </div>
                        {/volist}
                    </div>
                    <div class="flex space-x-4">
                        <a href="#featured" class="inline-flex items-center px-4 py-2 bg-white text-indigo-600 rounded-md font-medium hover:bg-indigo-50 transition-colors duration-200">
                            <i class="iconfont icon-fire mr-2"></i>
                            热门应用
                        </a>
                        <a href="#new" class="inline-flex items-center px-4 py-2 bg-indigo-500 text-white rounded-md font-medium hover:bg-indigo-400 transition-colors duration-200">
                            <i class="iconfont icon-new mr-2"></i>
                            最新上架
                        </a>
                    </div>
                </div>
                <div class="hidden md:block">
                    <img src="data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'><rect fill='none' width='200' height='200'/><path fill='rgba(255,255,255,0.3)' d='M0 0L200 0L200 200L0 200L0 0Z'/><path fill='rgba(255,255,255,0.6)' d='M40 40L160 40L160 160L40 160L40 40Z'/><circle fill='rgba(255,255,255,0.8)' cx='100' cy='100' r='40'/></svg>" alt="应用市场" class="w-48 h-48 object-contain">
                </div>
            </div>
        </div>
        <div class="absolute -bottom-10 -right-10 opacity-10">
            <i class="iconfont icon-app text-9xl"></i>
        </div>
    </div>

    <!-- 分类导航 -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-8">
        <div class="flex flex-wrap items-center justify-between">
            <div class="flex space-x-6 overflow-x-auto pb-2">
                <div class="category-tab active px-3 py-2 font-medium">全部应用</div>
                <div class="category-tab px-3 py-2 font-medium">移动应用</div>
                <div class="category-tab px-3 py-2 font-medium">桌面应用</div>
                <div class="category-tab px-3 py-2 font-medium">小程序</div>
                <div class="category-tab px-3 py-2 font-medium">热门推荐</div>
            </div>
            <div class="mt-2 sm:mt-0">
                <div class="relative">
                    <input type="text" placeholder="搜索应用..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <i class="iconfont icon-search text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 应用列表 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {volist name="list" id="item"}
        <div class="bg-white rounded-lg shadow-md overflow-hidden app-card relative">
            <!-- 价格标签 - 随机显示一些应用的价格 -->
            {php}
                $showPrice = rand(0, 1); // 随机决定是否显示价格
                $price = rand(10, 200); // 随机价格
            {/php}
            {if $showPrice}
            <div class="price-tag">¥{$price}</div>
            {/if}

            <a href="{__MPRE__}{:url('market/detail', ['appId' => $item.appId], false, false)}" class="block">
                <div class="p-4">
                    <div class="flex items-center mb-4">
                        <img src="{$item.logo}" alt="{$item.title}" class="w-16 h-16 rounded-lg mr-4 object-cover app-logo">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 line-clamp-1">{$item.title}</h3>
                            <div class="flex items-center">
                                <p class="text-sm text-gray-500 mr-2">AppCode: {$item.appCode}</p>
                                <div class="flex items-center">
                                    {php}
                                        $rating = rand(35, 50) / 10; // 随机评分 3.5-5.0
                                    {/php}
                                    <span class="text-yellow-500 text-xs">
                                        {for start="1" end="6"}
                                            <i class="iconfont {$i <= floor($rating) ? 'icon-star-fill' : ($i-0.5 <= $rating ? 'icon-star-half' : 'icon-star')}"></i>
                                        {/for}
                                    </span>
                                    <span class="text-xs text-gray-500 ml-1">{$rating}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4 line-clamp-3 app-description">{$item.description}</p>
                    <div class="flex flex-wrap gap-2 mb-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 app-tag">
                            <i class="iconfont icon-online mr-1"></i>
                            已上线
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$item.isPrivate ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'} app-tag">
                            <i class="iconfont {$item.isPrivate ? 'icon-lock' : 'icon-app'} mr-1"></i>
                            {$item.isPrivate ? '私有应用' : '公有应用'}
                        </span>
                        {php}
                            $tags = ['热门', '推荐', '新品', '限时'];
                            $showTag = rand(0, 1); // 随机决定是否显示额外标签
                            $tagIndex = rand(0, count($tags)-1); // 随机选择一个标签
                        {/php}
                        {if $showTag}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 app-tag">
                            <i class="iconfont icon-fire mr-1"></i>
                            {$tags[$tagIndex]}
                        </span>
                        {/if}
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-500">创建时间: {:date('Y-m-d', strtotime($item.createTime))}</span>
                        <div class="flex space-x-2">
                            <a href="{__MPRE__}{:url('market/detail', ['appId' => $item.appId], false, false)}" class="inline-flex items-center px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 transition-colors duration-200 buy-button">
                                <i class="iconfont icon-cart mr-1"></i>
                                立即购买
                            </a>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {/volist}
    </div>

    <!-- 推荐应用区域 -->
    <div class="mt-12 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="iconfont icon-fire text-red-500 mr-2"></i>
                热门推荐
            </h2>
            <a href="#" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                查看更多 <i class="iconfont icon-right"></i>
            </a>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {php}
                // 模拟4个热门推荐应用
                $hotApps = [];
                for ($i = 0; $i < 4; $i++) {
                    $hotApps[] = [
                        'title' => ['热门应用A', '推荐应用B', '精选应用C', '优质应用D'][$i],
                        'price' => rand(50, 200),
                        'discount' => rand(7, 9) . '折',
                        'rating' => rand(45, 50) / 10,
                        'sales' => rand(100, 999)
                    ];
                }
            {/php}

            {volist name="hotApps" id="hot"}
            <div class="bg-gradient-to-br from-indigo-50 to-white rounded-lg shadow-md overflow-hidden border border-indigo-100 hover:border-indigo-300 transition-all duration-300 transform hover:-translate-y-1">
                <div class="p-4">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-semibold text-gray-800">{$hot.title}</h3>
                        <span class="bg-red-500 text-white text-xs px-2 py-0.5 rounded">{$hot.discount}</span>
                    </div>
                    <div class="flex items-center mb-3">
                        <span class="text-yellow-500 text-xs">
                            {for start="1" end="6"}
                                <i class="iconfont {$i <= floor($hot.rating) ? 'icon-star-fill' : ($i-0.5 <= $hot.rating ? 'icon-star-half' : 'icon-star')}"></i>
                            {/for}
                        </span>
                        <span class="text-xs text-gray-500 ml-1">{$hot.rating}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-red-600 font-bold">¥{$hot.price}</span>
                        <span class="text-xs text-gray-500">已售 {$hot.sales}</span>
                    </div>
                </div>
            </div>
            {/volist}
        </div>
    </div>

    <!-- 分页 -->
    <div class="mt-8 flex justify-center">
        {$list|raw}
    </div>

    <!-- 底部促销信息 -->
    <!-- <div class="mt-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg p-6 text-white">
        <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="mb-4 md:mb-0">
                <h3 class="text-xl font-bold mb-2">限时优惠活动</h3>
                <p class="text-blue-100">注册即可获得优惠券，所有应用享受9折优惠！</p>
            </div>
            <a href="#" class="px-6 py-2 bg-white text-indigo-600 rounded-md font-medium hover:bg-indigo-50 transition-colors duration-200 shadow-md">
                立即注册
            </a>
        </div>
    </div> -->
</div>
{/block}
