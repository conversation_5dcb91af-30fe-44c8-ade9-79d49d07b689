<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\AppRelease as AppReleaseModel;
use app\model\AppBuild as AppBuildModel;
use app\validate\AppRelease as AppReleaseValidate;
use app\service\YmService;
use think\exception\ValidateException;

/**
 * AppRelease控制器
 */
class AppRelease extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\Auth',
        'app\\middleware\\AppCheck',
        'app\\middleware\\AppRuntimeCheck',
    ];

    /**
     * 发布列表
     */
    public function index(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境信息
        $appRuntime = $request->appRuntime;

        // 获取运行时环境ID
        $runtimeId = $request->param('runtimeId');

        // 获取YmService配置
        $ymConfig = YmService::getConfig();

        // 提取运行时环境名称
        $runtimeName = '';

        // 遍历配置中的运行时环境
        foreach ($ymConfig['runtime'] as $runtimeGroup) {
            foreach ($runtimeGroup['runtime'] as $runtime) {
                if ($runtime['id'] == $runtimeId) {
                    $runtimeName = $runtime['label'];
                    break 2;
                }
            }
        }

        $runtimeName = $runtimeName ?: '未知环境';

        // 获取搜索参数
        $search = [
            'description' => $request->param('description', ''),
            'isOnline' => $request->param('isOnline', ''),
            'appBuildId' => $request->param('appBuildId', ''),
        ];

        // 查询发布列表
        $list = AppReleaseModel::where('appRuntimeId', $appRuntime->appRuntimeId)
            ->withSearch(['description', 'isOnline', 'appBuildId'], $search)
            ->with(['build'])
            ->order('createTime', 'desc')
            ->paginate([
                'list_rows' => 10,
                'query' => $request->param(),
            ]);

        // 获取可用的应用包列表
        $builds = AppBuildModel::where('appId', $app->appId)
            ->where('status', 2) // 只获取审核通过的应用包
            ->field('appBuildId, versionName, versionCode')
            ->order('versionCode', 'desc')
            ->select();

        // 模板赋值
        View::assign([
            'app' => $app,
            'appRuntime' => $appRuntime,
            'runtimeId' => $runtimeId,
            'runtimeName' => $runtimeName,
            'list' => $list,
            'builds' => $builds,
            'search' => $search,
            'active' => 'release',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 创建发布
     */
    public function save(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境信息
        $appRuntime = $request->appRuntime;

        // 获取参数
        $data = $request->post();
        $data['appRuntimeId'] = $appRuntime->appRuntimeId;

        // 验证数据
        try {
            // 这里需要创建一个AppReleaseValidate类
            validate([
                'appBuildId' => 'require|number',
                'description' => 'require|max:512',
                'isOnline' => 'require|in:0,1',
            ])->check($data);
        } catch (ValidateException $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }

        // 检查应用包是否存在
        $build = AppBuildModel::where('appId', $app->appId)
            ->where('appBuildId', $data['appBuildId'])
            ->find();

        if (!$build) {
            return json(['code' => 0, 'msg' => '应用包不存在']);
        }

        // 创建发布
        $release = new AppReleaseModel();
        $release->save($data);

        // 返回结果
        return json(['code' => 1, 'msg' => '创建成功']);
    }

    /**
     * 更新发布状态
     */
    public function updateStatus(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境信息
        $appRuntime = $request->appRuntime;

        // 获取发布ID
        $appReleaseId = $request->param('appReleaseId');

        // 获取状态
        $isOnline = $request->param('isOnline');

        // 查询发布信息
        $release = AppReleaseModel::where('appReleaseId', $appReleaseId)
            ->where('appRuntimeId', $appRuntime->appRuntimeId)
            ->find();

        if (!$release) {
            return json(['code' => 0, 'msg' => '发布不存在']);
        }

        // 更新状态
        $release->isOnline = $isOnline;
        $release->save();

        // 返回结果
        return json(['code' => 1, 'msg' => '更新成功']);
    }

    /**
     * 删除发布
     */
    public function delete(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境信息
        $appRuntime = $request->appRuntime;

        // 获取发布ID
        $appReleaseId = $request->param('appReleaseId');

        // 查询发布信息
        $release = AppReleaseModel::where('appReleaseId', $appReleaseId)
            ->where('appRuntimeId', $appRuntime->appRuntimeId)
            ->find();

        if (!$release) {
            return json(['code' => 0, 'msg' => '发布不存在']);
        }

        // 删除发布
        $release->delete();

        // 返回结果
        return json(['code' => 1, 'msg' => '删除成功']);
    }
}
