<div id="sidebar" class="bg-white shadow rounded-lg p-4 w-full md:w-64 transition-all duration-300 ease-in-out">
    <!-- 移动端侧边栏标题栏 -->
    <div class="flex justify-between items-center mb-4 md:hidden">
        <h2 class="text-lg font-semibold text-gray-700 truncate">{$app.title}</h2>
        <button id="close-sidebar" class="text-gray-500 hover:text-gray-700 p-1">
            <i class="iconfont icon-close"></i>
        </button>
    </div>

    <div class="mb-4">
        <h2 class="text-lg font-semibold text-gray-700 truncate hidden md:block">{$app.title}</h2>
        <p class="text-sm text-gray-500">AppID: {$app.appId}</p>
    </div>
    <nav>
        <ul class="space-y-2">
            <li>
                <a href="{__MPRE__}{:url('app/detail', ['appId' => $app.appId], false, false)}" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {if $active=='detail'}bg-gray-100{/if}">
                    <i class="iconfont icon-detail mr-2"></i>
                    <span>应用详情</span>
                </a>
            </li>
            {php}
            // 获取YmService配置
            $ymConfig = \app\service\YmService::getConfig();

            // 提取运行时环境
            $ym_runtimes = [];
            foreach ($ymConfig['runtime'] as $runtimeGroup) {
                foreach ($runtimeGroup['runtime'] as $runtime) {
                    $ym_runtimes[] = $runtime;
                }
            }
            {/php}
            {if isset($ymConfig.icon)}
            <link rel="stylesheet" href="{$ymConfig.icon}">
            {/if}
            {volist name="ymConfig.runtime" id="gruntime"}
            <li>
                <div class="sidebar-toggle flex items-center justify-between px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 cursor-pointer">
                    <div class="flex items-center">
                        <i class="iconfont {$gruntime.icon} mr-2"></i>
                        <span>{$gruntime.label}运行时</span>
                    </div>
                    <i class="iconfont icon-down transform transition-transform duration-200 {if $active=='runtime'}rotate-180{/if}"></i>
                </div>
                {php}$hiddenname = 'hidden';{/php}
                {volist name="gruntime.runtime" id="runtime"}
                    {if isset($runtimeId) && $runtimeId == $runtime.id}
                    {php}$hiddenname = '';{/php}
                    {/if}
                {/volist}
                <ul class="pl-4 md:pl-8 mt-1 space-y-1 {$hiddenname} transition-all duration-300 ease-in-out">
                    {volist name="gruntime.runtime" id="runtime"}
                    <li>
                        <a href="{__MPRE__}{:url('appRuntime/edit', ['appId' => $app.appId, 'runtimeId' => $runtime.id], false, false)}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {if isset($runtimeId) && $runtimeId==$runtime.id}bg-gray-100{/if}">
                            <i class="iconfont {$runtime.icon} mr-2 text-gray-500"></i>
                            <span class="truncate">{$runtime.label}环境</span>
                        </a>
                    </li>
                    {/volist}
                </ul>
            </li>
            {/volist}
            <li>
                <a href="{__MPRE__}{:url('appBuild/index', ['appId' => $app.appId], false, false)}" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {if $active=='build'}bg-gray-100{/if}">
                    <i class="iconfont icon-package mr-2"></i>
                    <span>应用包管理</span>
                </a>
            </li>
            <li>
                <a href="{__MPRE__}{:url('appSku/index', ['appId' => $app.appId], false, false)}" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {if $active=='sku'}bg-gray-100{/if}">
                    <i class="iconfont icon-sku mr-2"></i>
                    <span>应用SKU管理</span>
                </a>
            </li>
            <li class="mt-4">
                <a href="{__MPRE__}{:url('app/index', [], false, false)}" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <i class="iconfont icon-back mr-2"></i>
                    <span>返回应用列表</span>
                </a>
            </li>
        </ul>
    </nav>
</div>

<!-- 移动端侧边栏遮罩层 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 hidden md:hidden"></div>
