{extend name="common/base" /}

{block name="title"}{$app.title} - {if $isEdit}编辑{else}创建{/if}SKU - 应用市场管理系统{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="iconfont {if $isEdit}icon-edit{else}icon-tag{/if} text-indigo-600 mr-2"></i>{if $isEdit}编辑{else}创建{/if}SKU
                </h2>
                <a href="{__MPRE__}{:url('appSku/index', ['appId' => $app.appId])}"
                    class="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs flex items-center">
                    <i class="iconfont icon-back mr-1"></i> 返回列表
                </a>
            </div>

            <form id="skuForm" data-validate>
                <!-- 基本信息 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-info-circle text-indigo-500 mr-2"></i>基本信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="name" class="block text-xs font-medium text-gray-700 mb-1">SKU名称 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-tag text-xs"></i>
                                </span>
                                <input type="text" id="name" name="name" value="{if $isEdit}{$appSku.name}{/if}" required maxlength="64" placeholder="输入SKU名称"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">如：Android, Android + iOS</p>
                        </div>

                        <div class="form-group">
                            <label for="description" class="block text-xs font-medium text-gray-700 mb-1">SKU说明</label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-description text-xs"></i>
                                </span>
                                <input type="text" id="description" name="description" value="{if $isEdit}{$appSku.description}{/if}"
                                    maxlength="256" placeholder="输入SKU说明" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格信息 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-price text-indigo-500 mr-2"></i>价格信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label for="salePrice" class="block text-xs font-medium text-gray-700 mb-1">售价 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-price text-xs"></i>
                                </span>
                                <input type="number" id="salePrice" name="salePrice" value="{if $isEdit}{$appSku.salePrice}{/if}"
                                    required min="0" step="0.01" placeholder="0.00" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="fullPrice" class="block text-xs font-medium text-gray-700 mb-1">原价 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-price text-xs"></i>
                                </span>
                                <input type="number" id="fullPrice" name="fullPrice" value="{if $isEdit}{$appSku.fullPrice}{/if}"
                                    required min="0" step="0.01" placeholder="0.00" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sort" class="block text-xs font-medium text-gray-700 mb-1">排序 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-sort text-xs"></i>
                                </span>
                                <input type="number" id="sort" name="sort" value="{if $isEdit}{$appSku.sort}{else}0{/if}" required min="0"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">数字越小排序越靠前</p>
                        </div>
                    </div>
                </div>

                <!-- 状态设置 -->
                <div class="mb-8">
                    <h2
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-setting text-indigo-500 mr-2"></i>状态设置
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-2">
                                上线状态 <span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-5 mt-1">
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="isOnline" value="0" {if !$isEdit || $appSku.isOnline == 0}checked{/if} class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span
                                        class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">未上线</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="isOnline" value="1" {if $isEdit && $appSku.isOnline == 1}checked{/if} class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span
                                        class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">已上线</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 环境配置 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-environment text-indigo-500 mr-2"></i>环境配置
                    </h3>
                    <div class="form-group">
                        <label class="block text-xs font-medium text-gray-700 mb-2">选择运行时环境 <span
                                class="text-red-500">*</span></label>
                        <div class="p-4 border border-gray-200 rounded-md bg-gray-50">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                {volist name="runtimes" id="runtime"}
                                <label
                                    class="relative flex items-center p-2 rounded-md border border-gray-200 bg-white hover:border-indigo-300 transition-colors duration-200 cursor-pointer group">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <input type="checkbox" name="runtimeIds[]" value="{$runtime.runtimeId}" {if
                                                in_array($runtime.runtimeId, $selectedRuntimeIds)}checked{/if}
                                                class="peer sr-only">
                                            <div class="w-4 h-4 border border-gray-300 rounded
                                                group-hover:border-indigo-400 peer-checked:bg-indigo-600
                                                peer-checked:border-indigo-600 transition-all duration-200"></div>
                                            <div
                                                class="absolute top-0 left-0 w-4 h-4 text-white flex items-center justify-center opacity-0 peer-checked:opacity-100">
                                                <i class="iconfont icon-check text-xs"></i>
                                            </div>
                                        </div>
                                        <div class="ml-2 text-sm">
                                            <div
                                                class="font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">
                                                {$runtime.runtimeName}</div>
                                        </div>
                                    </div>
                                </label>
                                {/volist}

                                {empty name="runtimes"}
                                <div
                                    class="md:col-span-3 p-4 bg-yellow-50 text-yellow-600 rounded-md flex items-center">
                                    <i class="iconfont icon-warning mr-2"></i> 请先创建运行时环境
                                </div>
                                {/empty}
                            </div>
                        </div>
                        <p class="mt-2 text-xs text-gray-500" id="selectedRuntimesCount">已选择 <span
                                class="font-medium text-indigo-600">0</span> 个环境</p>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-100">
                    <button type="button"
                        onclick="window.location.href='{__MPRE__}{:url(\'appSku/index\', [\'appId\' => $app.appId])}'"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-close mr-1"></i> 取消
                    </button>
                    <button type="button" id="submitButton" onclick="submitForm()" class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-save mr-1"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script src="{__MSTATIC__}/js/sku-form.js"></script>
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化SKU表单
        SkuForm.init({
            isEdit: {if $isEdit}true{else}false{/if},
            appId: '{$app.appId}',
            {if $isEdit}appSkuId: '{$appSku.appSkuId}',{/if}
            submitUrl: '{if $isEdit}{__MPRE__}{:url("appSku/update")}?appId={$app.appId}&appSkuId={$appSku.appSkuId}{else}{__MPRE__}{:url("appSku/save")}?appId={$app.appId}{/if}',
            indexUrl: '{__MPRE__}{:url("appSku/index", ["appId" => $app.appId])}'
        });
    });
</script>

<style>
    /* 添加动画 */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-5px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }

    .animate-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    /* 错误提示动画 */
    @keyframes shake {

        0%,
        100% {
            transform: translateX(0);
        }

        10%,
        30%,
        50%,
        70%,
        90% {
            transform: translateX(-5px);
        }

        20%,
        40%,
        60%,
        80% {
            transform: translateX(5px);
        }
    }

    .animate-shake {
        animation: shake 0.6s ease-in-out;
    }

    /* 输入框焦点效果 */
    .form-group.is-focused label {
        color: #4f46e5;
        /* indigo-600 */
    }

    /* 表单元素过渡效果 */
    input,
    select,
    textarea {
        transition: all 0.2s ease-in-out;
    }

    /* 通知动画 */
    #notification {
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease-in-out;
    }
</style>
{/block}
