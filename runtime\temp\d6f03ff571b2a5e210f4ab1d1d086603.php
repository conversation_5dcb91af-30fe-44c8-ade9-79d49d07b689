<?php /*a:5:{s:44:"E:\Ym\marketAdmin\app\view\app_sku\form.html";i:1748511908;s:43:"E:\Ym\marketAdmin\app\view\common\base.html";i:1747638572;s:45:"E:\Ym\marketAdmin\app\view\common\header.html";i:1747708602;s:46:"E:\Ym\marketAdmin\app\view\common\sidebar.html";i:1747708336;s:45:"E:\Ym\marketAdmin\app\view\common\footer.html";i:1747638354;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title><?php echo htmlentities((string) $app['title']); ?> - <?php if($isEdit): ?>编辑<?php else: ?>创建<?php endif; ?>SKU - 应用市场管理系统</title>
    <script src="/static/js/tw.js"></script>
    <link rel="stylesheet" href="/static/css/iconfont.css">
    <style>
        /* 基础响应式样式 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }
        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表格响应式样式 */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .touch-action-manipulation {
                touch-action: manipulation;
            }
            .tap-highlight-transparent {
                -webkit-tap-highlight-color: transparent;
            }
        }
    </style>
    
</head>
<body class="bg-gray-100 min-h-screen touch-action-manipulation tap-highlight-transparent">
    <!-- 移动端菜单按钮 -->
    <div id="mobile-menu-button" class="fixed z-50 bottom-4 right-4 md:hidden bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
        <i class="iconfont icon-menu text-xl"></i>
    </div>

    <!-- 头部 -->
    

    <!-- 主体内容 -->
    <div class="container mx-auto px-4 py-6">
        
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        <div id="sidebar" class="bg-white shadow rounded-lg p-4 w-full md:w-64 transition-all duration-300 ease-in-out">
    <!-- 移动端侧边栏标题栏 -->
    <div class="flex justify-between items-center mb-4 md:hidden">
        <h2 class="text-lg font-semibold text-gray-700 truncate"><?php echo htmlentities((string) $app['title']); ?></h2>
        <button id="close-sidebar" class="text-gray-500 hover:text-gray-700 p-1">
            <i class="iconfont icon-close"></i>
        </button>
    </div>

    <div class="mb-4">
        <h2 class="text-lg font-semibold text-gray-700 truncate hidden md:block"><?php echo htmlentities((string) $app['title']); ?></h2>
        <p class="text-sm text-gray-500">AppID: <?php echo htmlentities((string) $app['appId']); ?></p>
    </div>
    <nav>
        <ul class="space-y-2">
            <li>
                <a href="<?php echo url('app/detail', ['appId' => $app['appId']], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 <?php if($active=='detail'): ?>bg-gray-100<?php endif; ?>">
                    <i class="iconfont icon-detail mr-2"></i>
                    <span>应用详情</span>
                </a>
            </li>
            <?php 
            // 获取YmService配置
            $ymConfig = \app\service\YmService::getConfig();

            // 提取运行时环境
            $ym_runtimes = [];
            foreach ($ymConfig['runtime'] as $runtimeGroup) {
                foreach ($runtimeGroup['runtime'] as $runtime) {
                    $ym_runtimes[] = $runtime;
                }
            }
             if(isset($ymConfig['icon'])): ?>
            <link rel="stylesheet" href="<?php echo htmlentities((string) $ymConfig['icon']); ?>">
            <?php endif; if(is_array($ymConfig['runtime']) || $ymConfig['runtime'] instanceof \think\Collection || $ymConfig['runtime'] instanceof \think\Paginator): $i = 0; $__LIST__ = $ymConfig['runtime'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$gruntime): $mod = ($i % 2 );++$i;?>
            <li>
                <div class="sidebar-toggle flex items-center justify-between px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 cursor-pointer">
                    <div class="flex items-center">
                        <i class="iconfont <?php echo htmlentities((string) $gruntime['icon']); ?> mr-2"></i>
                        <span><?php echo htmlentities((string) $gruntime['label']); ?>运行时</span>
                    </div>
                    <i class="iconfont icon-down transform transition-transform duration-200 <?php if($active=='runtime'): ?>rotate-180<?php endif; ?>"></i>
                </div>
                <?php $hiddenname = 'hidden'; if(is_array($gruntime['runtime']) || $gruntime['runtime'] instanceof \think\Collection || $gruntime['runtime'] instanceof \think\Paginator): $i = 0; $__LIST__ = $gruntime['runtime'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$runtime): $mod = ($i % 2 );++$i;if(isset($runtimeId) && $runtimeId == $runtime['id']): $hiddenname = ''; ?>
                    <?php endif; ?>
                <?php endforeach; endif; else: echo "" ;endif; ?>
                <ul class="pl-4 md:pl-8 mt-1 space-y-1 <?php echo htmlentities((string) $hiddenname); ?> transition-all duration-300 ease-in-out">
                    <?php if(is_array($gruntime['runtime']) || $gruntime['runtime'] instanceof \think\Collection || $gruntime['runtime'] instanceof \think\Paginator): $i = 0; $__LIST__ = $gruntime['runtime'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$runtime): $mod = ($i % 2 );++$i;?>
                    <li>
                        <a href="<?php echo url('appRuntime/edit', ['appId' => $app['appId'], 'runtimeId' => $runtime['id']], false, false); ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 <?php if(isset($runtimeId) && $runtimeId==$runtime['id']): ?>bg-gray-100<?php endif; ?>">
                            <i class="iconfont <?php echo htmlentities((string) $runtime['icon']); ?> mr-2 text-gray-500"></i>
                            <span class="truncate"><?php echo htmlentities((string) $runtime['label']); ?>环境</span>
                        </a>
                    </li>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </ul>
            </li>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            <li>
                <a href="<?php echo url('appBuild/index', ['appId' => $app['appId']], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 <?php if($active=='build'): ?>bg-gray-100<?php endif; ?>">
                    <i class="iconfont icon-package mr-2"></i>
                    <span>应用包管理</span>
                </a>
            </li>
            <li>
                <a href="<?php echo url('appSku/index', ['appId' => $app['appId']], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 <?php if($active=='sku'): ?>bg-gray-100<?php endif; ?>">
                    <i class="iconfont icon-sku mr-2"></i>
                    <span>应用SKU管理</span>
                </a>
            </li>
            <li class="mt-4">
                <a href="<?php echo url('app/index', [], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <i class="iconfont icon-back mr-2"></i>
                    <span>返回应用列表</span>
                </a>
            </li>
        </ul>
    </nav>
</div>

<!-- 移动端侧边栏遮罩层 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 hidden md:hidden"></div>

    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="iconfont <?php if($isEdit): ?>icon-edit<?php else: ?>icon-tag<?php endif; ?> text-indigo-600 mr-2"></i><?php if($isEdit): ?>编辑<?php else: ?>创建<?php endif; ?>SKU
                </h2>
                <a href="<?php echo url('appSku/index', ['appId' => $app['appId']]); ?>"
                    class="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs flex items-center">
                    <i class="iconfont icon-back mr-1"></i> 返回列表
                </a>
            </div>

            <form id="skuForm" data-validate>
                <!-- 基本信息 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-info-circle text-indigo-500 mr-2"></i>基本信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="name" class="block text-xs font-medium text-gray-700 mb-1">SKU名称 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-tag text-xs"></i>
                                </span>
                                <input type="text" id="name" name="name" value="<?php if($isEdit): ?><?php echo htmlentities((string) $appSku['name']); ?><?php endif; ?>" required maxlength="64" placeholder="输入SKU名称"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">如：Android, Android + iOS</p>
                        </div>

                        <div class="form-group">
                            <label for="description" class="block text-xs font-medium text-gray-700 mb-1">SKU说明</label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-description text-xs"></i>
                                </span>
                                <input type="text" id="description" name="description" value="<?php if($isEdit): ?><?php echo htmlentities((string) $appSku['description']); ?><?php endif; ?>"
                                    maxlength="256" placeholder="输入SKU说明" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格信息 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-price text-indigo-500 mr-2"></i>价格信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label for="salePrice" class="block text-xs font-medium text-gray-700 mb-1">售价 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-price text-xs"></i>
                                </span>
                                <input type="number" id="salePrice" name="salePrice" value="<?php if($isEdit): ?><?php echo htmlentities((string) $appSku['salePrice']); ?><?php endif; ?>"
                                    required min="0" step="0.01" placeholder="0.00" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="fullPrice" class="block text-xs font-medium text-gray-700 mb-1">原价 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-price text-xs"></i>
                                </span>
                                <input type="number" id="fullPrice" name="fullPrice" value="<?php if($isEdit): ?><?php echo htmlentities((string) $appSku['fullPrice']); ?><?php endif; ?>"
                                    required min="0" step="0.01" placeholder="0.00" class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sort" class="block text-xs font-medium text-gray-700 mb-1">排序 <span
                                    class="text-red-500">*</span></label>
                            <div class="relative">
                                <span
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-sort text-xs"></i>
                                </span>
                                <input type="number" id="sort" name="sort" value="<?php if($isEdit): ?><?php echo htmlentities((string) $appSku['sort']); else: ?>0<?php endif; ?>" required min="0"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">数字越小排序越靠前</p>
                        </div>
                    </div>
                </div>

                <!-- 状态设置 -->
                <div class="mb-8">
                    <h2
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-setting text-indigo-500 mr-2"></i>状态设置
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-2">
                                上线状态 <span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-5 mt-1">
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="isOnline" value="0" <?php if(!$isEdit || $appSku['isOnline'] == 0): ?>checked<?php endif; ?> class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span
                                        class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">未上线</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="isOnline" value="1" <?php if($isEdit && $appSku['isOnline'] == 1): ?>checked<?php endif; ?> class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span
                                        class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">已上线</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 环境配置 -->
                <div class="mb-8">
                    <h3
                        class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-environment text-indigo-500 mr-2"></i>环境配置
                    </h3>
                    <div class="form-group">
                        <label class="block text-xs font-medium text-gray-700 mb-2">选择运行时环境 <span
                                class="text-red-500">*</span></label>
                        <div class="p-4 border border-gray-200 rounded-md bg-gray-50">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <?php if(is_array($runtimes) || $runtimes instanceof \think\Collection || $runtimes instanceof \think\Paginator): $i = 0; $__LIST__ = $runtimes;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$runtime): $mod = ($i % 2 );++$i;?>
                                <label
                                    class="relative flex items-center p-2 rounded-md border border-gray-200 bg-white hover:border-indigo-300 transition-colors duration-200 cursor-pointer group">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <input type="checkbox" name="runtimeIds[]" value="<?php echo htmlentities((string) $runtime['runtimeId']); ?>" <?php if(in_array($runtime['runtimeId'], $selectedRuntimeIds)): ?>checked<?php endif; ?>
                                                class="peer sr-only">
                                            <div class="w-4 h-4 border border-gray-300 rounded
                                                group-hover:border-indigo-400 peer-checked:bg-indigo-600
                                                peer-checked:border-indigo-600 transition-all duration-200"></div>
                                            <div
                                                class="absolute top-0 left-0 w-4 h-4 text-white flex items-center justify-center opacity-0 peer-checked:opacity-100">
                                                <i class="iconfont icon-check text-xs"></i>
                                            </div>
                                        </div>
                                        <div class="ml-2 text-sm">
                                            <div
                                                class="font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">
                                                <?php echo htmlentities((string) $runtime['runtimeName']); ?></div>
                                        </div>
                                    </div>
                                </label>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($runtimes) || (($runtimes instanceof \think\Collection || $runtimes instanceof \think\Paginator ) && $runtimes->isEmpty())): ?>
                                <div
                                    class="md:col-span-3 p-4 bg-yellow-50 text-yellow-600 rounded-md flex items-center">
                                    <i class="iconfont icon-warning mr-2"></i> 请先创建运行时环境
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <p class="mt-2 text-xs text-gray-500" id="selectedRuntimesCount">已选择 <span
                                class="font-medium text-indigo-600">0</span> 个环境</p>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-100">
                    <button type="button"
                        onclick="window.location.href='<?php echo url('appSku/index', ['appId' => $app['appId']]); ?>'"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-close mr-1"></i> 取消
                    </button>
                    <button type="button" id="submitButton" onclick="submitForm()" class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-save mr-1"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

    </div>

    <!-- 底部 -->
    <footer class="bg-white shadow mt-8 py-4">
    <div class="container mx-auto px-4">
        <div class="text-center text-gray-500 text-sm">
            &copy; 2025 应用市场管理系统 
        </div>
    </div>
</footer>


    <script src="/static/js/app.js"></script>
    
<script src="/static/js/sku-form.js"></script>
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化SKU表单
        SkuForm.init({
            isEdit: <?php if($isEdit): ?>true<?php else: ?>false<?php endif; ?>,
            appId: '<?php echo htmlentities((string) $app['appId']); ?>',
            <?php if($isEdit): ?>appSkuId: '<?php echo htmlentities((string) $appSku['appSkuId']); ?>',<?php endif; ?>
            submitUrl: '<?php if($isEdit): ?><?php echo url("appSku/update"); ?>?appId=<?php echo htmlentities((string) $app['appId']); ?>&appSkuId=<?php echo htmlentities((string) $appSku['appSkuId']); else: ?><?php echo url("appSku/save"); ?>?appId=<?php echo htmlentities((string) $app['appId']); ?><?php endif; ?>',
            indexUrl: '<?php echo url("appSku/index", ["appId" => $app['appId']]); ?>'
        });
    });
</script>

<style>
    /* 添加动画 */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-5px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }

    .animate-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    /* 错误提示动画 */
    @keyframes shake {

        0%,
        100% {
            transform: translateX(0);
        }

        10%,
        30%,
        50%,
        70%,
        90% {
            transform: translateX(-5px);
        }

        20%,
        40%,
        60%,
        80% {
            transform: translateX(5px);
        }
    }

    .animate-shake {
        animation: shake 0.6s ease-in-out;
    }

    /* 输入框焦点效果 */
    .form-group.is-focused label {
        color: #4f46e5;
        /* indigo-600 */
    }

    /* 表单元素过渡效果 */
    input,
    select,
    textarea {
        transition: all 0.2s ease-in-out;
    }

    /* 通知动画 */
    #notification {
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease-in-out;
    }
</style>

</body>
</html>
