<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;
use think\facade\Db;

/**
 * AppSku模型
 */
class AppSku extends Model
{
    // 设置表名
    protected $name = 'app_sku';

    // 设置主键
    protected $pk = 'appSkuId';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 设置创建时间字段
    protected $createTime = 'createTime';

    // 设置更新时间字段
    protected $updateTime = false;

    /**
     * 获取关联的运行时环境
     */
    public function runtimes()
    {
        return $this->belongsToMany(AppRuntime::class, 'app_sku_runtime', 'appSkuId', 'appRuntimeId');
    }

    /**
     * 获取应用ID
     * 通过关联的运行时环境获取应用ID
     */
    public function getAppIdByRuntimes()
    {
        // 获取关联的运行时环境ID列表
        $appRuntimeIds = $this->runtimes()->column('appRuntimeId');

        if (empty($appRuntimeIds)) {
            return null;
        }

        // 通过运行时环境ID获取应用ID
        $appId = AppRuntime::whereIn('appRuntimeId', $appRuntimeIds)
            ->value('appId');

        return $appId;
    }

    /**
     * 获取特定应用下的SKU列表
     *
     * @param int $appId 应用ID
     * @param array $search 搜索条件
     * @return \think\Collection
     */
    public static function getSkusByAppId($appId, $search = [])
    {
        // 获取应用下的所有运行时环境ID
        $appRuntimeIds = AppRuntime::where('appId', $appId)->column('appRuntimeId');

        if (empty($appRuntimeIds)) {
            return self::where('1=0'); // 返回空集合
        }

        // 获取关联的SKU ID列表
        $appSkuIds = Db::name('app_sku_runtime')
            ->whereIn('appRuntimeId', $appRuntimeIds)
            ->group('appSkuId')
            ->column('appSkuId');

        if (empty($appSkuIds)) {
            return self::where('1=0'); // 返回空集合
        }

        // 构建查询
        $query = self::whereIn('appSkuId', $appSkuIds);

        // 应用搜索条件
        if (!empty($search['name'])) {
            $query->where('name', 'like', "%{$search['name']}%");
        }

        if (!empty($search['priceRange']) && is_array($search['priceRange'])) {
            if (isset($search['priceRange'][0]) && $search['priceRange'][0] !== '') {
                $query->where('salePrice', '>=', $search['priceRange'][0]);
            }

            if (isset($search['priceRange'][1]) && $search['priceRange'][1] !== '') {
                $query->where('salePrice', '<=', $search['priceRange'][1]);
            }
        }
        return $query;
    }

    /**
     * 检查SKU是否属于特定应用
     *
     * @param int $appSkuId SKU ID
     * @param int $appId 应用ID
     * @return bool
     */
    public static function checkSkuBelongsToApp($appSkuId, $appId)
    {
        // 获取应用下的所有运行时环境ID
        $appRuntimeIds = AppRuntime::where('appId', $appId)->column('appRuntimeId');

        if (empty($appRuntimeIds)) {
            return false;
        }

        // 检查是否存在关联
        $exists = Db::name('app_sku_runtime')
            ->where('appSkuId', $appSkuId)
            ->whereIn('appRuntimeId', $appRuntimeIds)
            ->count();

        return $exists > 0;
    }

    /**
     * 搜索器：按名称搜索
     */
    public function searchNameAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('name', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按价格范围搜索
     */
    public function searchPriceRangeAttr($query, $value)
    {
        if (!empty($value) && is_array($value)) {
            if (isset($value[0]) && $value[0] !== '') {
                $query->where('salePrice', '>=', $value[0]);
            }

            if (isset($value[1]) && $value[1] !== '') {
                $query->where('salePrice', '<=', $value[1]);
            }
        }
    }
}
