{extend name="common/base" /}

{block name="title"}{$app.title} - SKU管理 - 应用市场管理系统{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 p-2 rounded-lg shadow-md mr-3">
                        <i class="iconfont icon-sku text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800">SKU管理</h2>
                        <p class="text-sm text-gray-500 mt-1">管理应用的SKU配置和价格</p>
                    </div>
                </div>
                <a href="{__MPRE__}{:url('appSku/create', ['appId' => $app.appId])}"
                   class="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-md
                   hover:from-green-600 hover:to-green-700 shadow-sm transition-all duration-300
                   transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500
                   flex items-center">
                    <i class="iconfont icon-add mr-1"></i> 新建SKU
                </a>
            </div>

            <!-- 搜索和筛选 -->
            <div class="mb-8">
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-1"></div>
                    <div class="p-5">
                        <div class="flex items-center mb-4">
                            <i class="iconfont icon-filter text-indigo-500 text-lg mr-2"></i>
                            <h3 class="text-sm font-semibold text-gray-700">筛选条件</h3>
                            <div class="ml-auto">
                                <button type="button" id="toggleFilterBtn" class="text-xs text-indigo-600 hover:text-indigo-800 flex items-center">
                                    <i class="iconfont icon-down mr-1 transform transition-transform duration-300" id="toggleFilterIcon"></i>
                                    <span id="toggleFilterText">收起筛选</span>
                                </button>
                            </div>
                        </div>

                        <form id="filterForm" action="{__MPRE__}{:url('appSku/index', ['appId' => $app.appId])}" method="get" class="transition-all duration-300">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                                <div class="form-group">
                                    <label for="name" class="block text-xs font-medium text-gray-700 mb-1">SKU名称</label>
                                    <div class="relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="iconfont icon-tag text-gray-400 text-xs"></i>
                                        </div>
                                        <input type="text" id="name" name="name" value="{$search.name}"
                                            placeholder="搜索SKU名称"
                                            class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                            hover:border-indigo-300 transition-colors duration-200">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="block text-xs font-medium text-gray-700 mb-1">价格区间</label>
                                    <div class="flex items-center space-x-2">
                                        <div class="relative flex-1 rounded-md shadow-sm">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-400 text-xs">¥</span>
                                            </div>
                                            <input type="number" id="minPrice" name="minPrice" value="{$search.priceRange.0}" min="0" step="0.01"
                                                placeholder="最低价"
                                                class="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                                focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                                hover:border-indigo-300 transition-colors duration-200">
                                        </div>
                                        <span class="text-gray-400">-</span>
                                        <div class="relative flex-1 rounded-md shadow-sm">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-400 text-xs">¥</span>
                                            </div>
                                            <input type="number" id="maxPrice" name="maxPrice" value="{$search.priceRange.1}" min="0" step="0.01"
                                                placeholder="最高价"
                                                class="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                                focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                                hover:border-indigo-300 transition-colors duration-200">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group md:flex md:items-end">
                                    <div class="hidden md:block w-full">
                                        <label class="block text-xs font-medium text-gray-700 mb-1 opacity-0">操作</label>
                                        <div class="flex space-x-2">
                                            <button type="submit"
                                                class="flex-1 px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                                                hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                                focus:ring-offset-1 focus:ring-indigo-500 flex items-center justify-center">
                                                <i class="iconfont icon-search mr-1"></i> 搜索
                                            </button>
                                            <button type="button" onclick="resetForm()"
                                                class="flex-1 px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                                                hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                                                focus:ring-offset-1 focus:ring-gray-400 flex items-center justify-center">
                                                <i class="iconfont icon-refresh mr-1"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 移动端按钮 -->
                            <div class="mt-4 flex space-x-2 md:hidden">
                                <button type="submit"
                                    class="flex-1 px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                                    hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                    focus:ring-offset-1 focus:ring-indigo-500 flex items-center justify-center">
                                    <i class="iconfont icon-search mr-1"></i> 搜索
                                </button>
                                <button type="button" onclick="resetForm()"
                                    class="flex-1 px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                                    hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                                    focus:ring-offset-1 focus:ring-gray-400 flex items-center justify-center">
                                    <i class="iconfont icon-refresh mr-1"></i> 重置
                                </button>
                            </div>

                            <input type="hidden" name="appId" value="{$app.appId}">
                        </form>
                    </div>
                </div>
            </div>

            <!-- SKU列表 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {volist name="list" id="item"}
                <div class="bg-white border rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300 transform hover:-translate-y-1 transition-transform duration-300">
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-2"></div>
                    <div class="p-5">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="text-lg font-semibold text-gray-800">{$item.name}</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="iconfont icon-sku mr-1"></i> SKU
                            </span>
                        </div>

                        {if $item.description}
                        <div class="mb-4">
                            <p class="text-gray-600 line-clamp-2">{$item.description}</p>
                        </div>
                        {/if}

                        <div class="flex justify-between items-center mb-4 pb-4 border-b border-gray-100">
                            <div class="flex items-center">
                                <span class="text-xl font-bold text-red-600">¥{$item.salePrice}</span>
                                {if $item.fullPrice > $item.salePrice}
                                <span class="ml-2 text-sm text-gray-500 line-through">¥{$item.fullPrice}</span>
                                <span class="ml-2 text-xs px-1.5 py-0.5 bg-red-100 text-red-800 rounded">
                                    {php}echo round(($item['salePrice']/$item['fullPrice'])*10, 1);{/php}折
                                </span>
                                {/if}
                            </div>
                            <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                <i class="iconfont icon-package text-gray-500 mr-1"></i>
                                <span class="text-sm text-gray-600">付费: {$item.paidCount}</span>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="iconfont icon-filter text-gray-400 mr-1"></i>
                                <span class="text-sm text-gray-500">排序: {$item.sort}</span>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{__MPRE__}{:url('appSku/edit', ['appId' => $app.appId, 'appSkuId' => $item.appSkuId])}" class="px-2 py-1 text-xs bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100 transition-colors duration-200 flex items-center">
                                    <i class="iconfont icon-edit mr-1"></i> 编辑
                                </a>
                                <button type="button" class="delete-sku-btn px-2 py-1 text-xs bg-red-50 text-red-600 rounded hover:bg-red-100 transition-colors duration-200 flex items-center" data-id="{$item.appSkuId}">
                                    <i class="iconfont icon-delete mr-1"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {/volist}

                {empty name="list"}
                <div class="md:col-span-3 p-8 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="iconfont icon-sku text-5xl text-gray-300 mb-3"></i>
                        <p class="text-gray-500">暂无SKU数据</p>
                        <a href="{__MPRE__}{:url('appSku/create', ['appId' => $app.appId])}" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="iconfont icon-add"></i> 创建新SKU
                        </a>
                    </div>
                </div>
                {/empty}
            </div>

            <!-- 分页 -->
            <div class="mt-6">
                {$list|raw}
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化筛选表单
    initFilterForm();

    // 初始化表单元素
    initFormElements();

    // 初始化删除按钮
    initDeleteButtons();
});

// 初始化筛选表单
function initFilterForm() {
    const toggleBtn = document.getElementById('toggleFilterBtn');
    const filterForm = document.getElementById('filterForm');
    const toggleIcon = document.getElementById('toggleFilterIcon');
    const toggleText = document.getElementById('toggleFilterText');

    if (toggleBtn && filterForm) {
        // 检查是否有筛选条件
        const hasFilters = checkHasFilters();

        // 如果没有筛选条件，默认收起筛选表单
        if (!hasFilters) {
            toggleFilter();
        }

        // 添加点击事件
        toggleBtn.addEventListener('click', toggleFilter);
    }

    function toggleFilter() {
        const formContent = filterForm.querySelector('.grid');
        const mobileButtons = filterForm.querySelector('.mt-4.flex');

        if (formContent.classList.contains('hidden')) {
            // 展开筛选表单
            formContent.classList.remove('hidden');
            if (mobileButtons) mobileButtons.classList.remove('hidden');
            toggleIcon.classList.remove('rotate-180');
            toggleText.textContent = '收起筛选';
        } else {
            // 收起筛选表单
            formContent.classList.add('hidden');
            if (mobileButtons) mobileButtons.classList.add('hidden');
            toggleIcon.classList.add('rotate-180');
            toggleText.textContent = '展开筛选';
        }
    }

    // 检查是否有筛选条件
    function checkHasFilters() {
        const name = document.getElementById('name').value;
        const minPrice = document.getElementById('minPrice').value;
        const maxPrice = document.getElementById('maxPrice').value;

        return name || minPrice || maxPrice;
    }
}

// 初始化表单元素
function initFormElements() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], input[type="number"]');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');
        });
    });

    // 价格输入框特殊处理
    const priceInputs = document.querySelectorAll('input[type="number"][step="0.01"]');
    priceInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value) {
                this.value = parseFloat(this.value).toFixed(2);
            }
        });
    });
}

// 重置筛选表单
function resetForm() {
    const form = document.getElementById('filterForm');
    if (form) {
        // 清空所有输入框
        const inputs = form.querySelectorAll('input[type="text"], input[type="number"]');
        inputs.forEach(input => {
            input.value = '';
        });

        // 提交表单
        form.submit();
    }
}

// 初始化删除按钮
function initDeleteButtons() {
    const deleteButtons = document.querySelectorAll('.delete-sku-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const appSkuId = this.getAttribute('data-id');
            deleteSku(appSkuId);
        });
    });
}

// 删除SKU
async function deleteSku(appSkuId) {
    // 显示确认对话框
    if (confirm('确定要删除这个SKU吗？此操作不可恢复！')) {
        try {
            // 显示加载状态
            showNotification('正在删除...', 'info');

            // 发送请求
            const response = await fetch('{__MPRE__}{:url("appSku/delete")}?appId={$app.appId}&appSkuId=' + appSkuId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.code === 1) {
                showNotification(data.msg, 'success');

                // 找到对应的SKU卡片并添加淡出效果
                const skuCard = document.querySelector(`.delete-sku-btn[data-id="${appSkuId}"]`).closest('.bg-white.border.rounded-lg');
                if (skuCard) {
                    skuCard.style.transition = 'all 0.5s ease-out';
                    skuCard.style.opacity = '0';
                    skuCard.style.transform = 'scale(0.9)';
                }

                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.msg || '删除失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('删除失败，请重试', 'error');
        }
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 输入框焦点效果 */
.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select, textarea {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}
</style>
{/block}
